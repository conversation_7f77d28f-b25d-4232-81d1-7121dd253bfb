-- Test script to check access logging functionality
-- You can run this in your Supabase SQL editor to see recent logs

-- Check recent access logs
SELECT 
    id,
    user_id,
    guest_name,
    timestamp,
    created_at,
    CASE 
        WHEN user_id IS NOT NULL THEN 'Authenticated User'
        WHEN guest_name IS NOT NULL THEN 'Guest User (' || guest_name || ')'
        ELSE 'Unknown'
    END AS user_type
FROM user_logs
ORDER BY timestamp DESC
LIMIT 20;

-- Count logs by type
SELECT 
    CASE 
        WHEN user_id IS NOT NULL THEN 'Authenticated'
        WHEN guest_name IS NOT NULL THEN 'Guest'
        ELSE 'Unknown'
    END AS user_type,
    COUNT(*) as log_count
FROM user_logs
GROUP BY user_type;

-- Recent authenticated user activity
SELECT 
    u.email,
    ul.timestamp,
    COUNT(*) as access_count
FROM user_logs ul
JOIN auth.users u ON ul.user_id = u.id
WHERE ul.timestamp > NOW() - INTERVAL '1 hour'
GROUP BY u.email, ul.timestamp
ORDER BY ul.timestamp DESC;
