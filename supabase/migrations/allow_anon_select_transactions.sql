-- Allow anonymous (unauthenticated) users to view transactions
-- This enables read-only browsing for guests while keeping writes restricted.

-- Remove any existing overly restrictive SELECT policies for anon if necessary
-- (No-op if none found)
DROP POLICY IF EXISTS "Anonymous users can view transactions" ON public.transactions;

-- Create a policy that grants SELECT to anon role
CREATE POLICY "Anonymous users can view transactions"
  ON public.transactions
  FOR SELECT
  TO anon
  USING (true);

-- Note: Other policies for INSERT/UPDATE/DELETE remain intact and restricted to admins.
