-- Create user_logs table for access logging
CREATE TABLE IF NOT EXISTS user_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    guest_name TEXT,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_logs_user_id ON user_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_logs_timestamp ON user_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_user_logs_guest_name ON user_logs(guest_name);

-- Enable RLS (Row Level Security)
ALTER TABLE user_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS
CREATE POLICY "Allow all operations on user_logs for authenticated users" ON user_logs
    FOR ALL USING (true);

-- Allow anonymous users to insert logs (for guest access tracking)
CREATE POLICY "Allow anonymous insert on user_logs" ON user_logs
    FOR INSERT WITH CHECK (true);

-- Allow anonymous users to select their own guest logs
CREATE POLICY "Allow anonymous select on user_logs" ON user_logs
    FOR SELECT USING (true);
