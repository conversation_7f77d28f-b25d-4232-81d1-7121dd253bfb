-- Create transactions table with all required fields
CREATE TABLE public.transactions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  type TEXT NOT NULL CHECK (type IN ('money-in', 'money-out')),
  amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
  note TEXT,  -- Made nullable since users might not always want to add notes
  category TEXT,  -- Added for future expense categorization (food, family, shopping, etc.)
  date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  balance DECIMAL(15,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

-- Create policies (allowing all operations for now - update when auth is implemented)
CREATE POLICY "Enable read access for all users" ON public.transactions
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for all users" ON public.transactions
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for all users" ON public.transactions
  FOR UPDATE USING (true);

CREATE POLICY "Enable delete for all users" ON public.transactions
  FOR DELETE USING (true);

-- Create indexes for better performance
CREATE INDEX idx_transactions_date ON public.transactions(date DESC);
CREATE INDEX idx_transactions_type ON public.transactions(type);
CREATE INDEX idx_transactions_category ON public.transactions(category);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_transactions_updated_at
  BEFORE UPDATE ON public.transactions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Add helpful comments
COMMENT ON TABLE public.transactions IS 'Monthly expense management transactions';
COMMENT ON COLUMN public.transactions.type IS 'Transaction type: money-in or money-out';
COMMENT ON COLUMN public.transactions.amount IS 'Transaction amount in VND';
COMMENT ON COLUMN public.transactions.note IS 'Optional note/description for the transaction';
COMMENT ON COLUMN public.transactions.category IS 'Expense category for analysis (e.g., food, family, shopping, transport, entertainment, etc.)';
COMMENT ON COLUMN public.transactions.balance IS 'Account balance after this transaction';
COMMENT ON COLUMN public.transactions.date IS 'When the transaction occurred';