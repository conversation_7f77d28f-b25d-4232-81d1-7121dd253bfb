-- Insert sample posts data to replace the hardcoded data
INSERT INTO public.posts (id, user_name, avatar, content, likes_count, created_at) VALUES
  (
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'Nguyễn <PERSON>n <PERSON>',
    '/placeholder.svg',
    'Hôm nay tôi đã tiết kiệm được 100,000 VND bằng cách nấu ăn tại nhà thay vì đi ăn ngoài. Cảm thấy rất hài lòng! 🍳✨ Ai cũng nên thử nhé!',
    24,
    now() - interval '2 hours'
  ),
  (
    'b2c3d4e5-f6g7-8901-bcde-f23456789012',
    'Lê Thị C',
    '/placeholder.svg',
    'Ai có mẹo nào để quản lý chi tiêu hàng tháng hiệu quả không? Tôi đang gặp khó khăn trong việc cân bằng thu chi. <PERSON><PERSON><PERSON> người chia sẻ kinh nghiệm giúp mình với! 🙏',
    18,
    now() - interval '5 hours'
  ),
  (
    'c3d4e5f6-g7h8-9012-cdef-345678901234',
    'Trần Minh F',
    '/placeholder.svg',
    'Vừa đạt được mục tiêu tiết kiệm 5 triệu trong tháng này! 🎉 Bí quyết là cắt giảm những chi tiêu không cần thiết và đầu tư vào những thứ thực sự quan trọng.',
    35,
    now() - interval '8 hours'
  );

-- Insert sample comments data
INSERT INTO public.post_comments (post_id, user_name, content, created_at) VALUES
  -- Comments for first post
  (
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'Trần Thị B',
    'Tuyệt vời! Nấu ăn tại nhà vừa tiết kiệm vừa healthy. Mình cũng đang áp dụng 👍',
    now() - interval '1 hour'
  ),
  (
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'Lê Minh C',
    'Wow, 100k một ngày là khá nhiều đấy! Mình cũng muốn học nấu ăn quá',
    now() - interval '45 minutes'
  ),
  -- Comments for second post
  (
    'b2c3d4e5-f6g7-8901-bcde-f23456789012',
    'Phạm Văn D',
    'Tôi dùng app này để theo dõi, rất hiệu quả! Nhớ phân loại chi tiêu từng mục nha',
    now() - interval '4 hours'
  ),
  (
    'b2c3d4e5-f6g7-8901-bcde-f23456789012',
    'Hoàng Thị E',
    'Nên lập kế hoạch chi tiêu từ đầu tháng và tuân thủ nghiêm ngặt. Rule 50/30/20 rất hay đó!',
    now() - interval '3 hours'
  ),
  -- Comments for third post
  (
    'c3d4e5f6-g7h8-9012-cdef-345678901234',
    'Nguyễn Thị G',
    'Chúc mừng bạn! Chia sẻ bí quyết cụ thể được không? 🤔',
    now() - interval '7 hours'
  );
