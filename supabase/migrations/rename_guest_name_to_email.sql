-- Migration to rename guest_name column to email in user_logs table
-- This migration changes the column name to better reflect that it stores email addresses

-- Drop the existing index on guest_name
DROP INDEX IF EXISTS idx_user_logs_guest_name;

-- Rename the column from guest_name to email
ALTER TABLE user_logs RENAME COLUMN guest_name TO email;

-- Create new index for the email column
CREATE INDEX IF NOT EXISTS idx_user_logs_email ON user_logs(email);

-- Add a comment to document the column purpose
COMMENT ON COLUMN user_logs.email IS 'Email address for both authenticated users and guest users (from localStorage)';
