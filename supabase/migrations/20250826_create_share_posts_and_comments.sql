-- Create posts table for sharing functionality
CREATE TABLE public.posts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_name TEXT NOT NULL,
  avatar TEXT,
  content TEXT NOT NULL,
  image TEXT,
  likes_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create comments table for post comments
CREATE TABLE public.post_comments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES public.posts(id) ON DELETE CASCADE,
  user_name TEXT NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create post_likes table to track likes (for future user-specific likes tracking)
CREATE TABLE public.post_likes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES public.posts(id) ON DELETE CASCADE,
  user_name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(post_id, user_name)
);

-- Enable Row Level Security
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_likes ENABLE ROW LEVEL SECURITY;

-- Create policies for posts (allowing all operations for now)
CREATE POLICY "Enable read access for all users" ON public.posts
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for all users" ON public.posts
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for all users" ON public.posts
  FOR UPDATE USING (true);

CREATE POLICY "Enable delete for all users" ON public.posts
  FOR DELETE USING (true);

-- Create policies for comments
CREATE POLICY "Enable read access for all users" ON public.post_comments
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for all users" ON public.post_comments
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for all users" ON public.post_comments
  FOR UPDATE USING (true);

CREATE POLICY "Enable delete for all users" ON public.post_comments
  FOR DELETE USING (true);

-- Create policies for likes
CREATE POLICY "Enable read access for all users" ON public.post_likes
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for all users" ON public.post_likes
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable delete for all users" ON public.post_likes
  FOR DELETE USING (true);

-- Create indexes for better performance
CREATE INDEX idx_posts_created_at ON public.posts(created_at DESC);
CREATE INDEX idx_post_comments_post_id ON public.post_comments(post_id);
CREATE INDEX idx_post_comments_created_at ON public.post_comments(created_at ASC);
CREATE INDEX idx_post_likes_post_id ON public.post_likes(post_id);
CREATE INDEX idx_post_likes_user_name ON public.post_likes(user_name);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_posts_updated_at
  BEFORE UPDATE ON public.posts
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_post_comments_updated_at
  BEFORE UPDATE ON public.post_comments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Function to update likes count when likes are added/removed
CREATE OR REPLACE FUNCTION public.update_post_likes_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE public.posts 
    SET likes_count = likes_count + 1 
    WHERE id = NEW.post_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE public.posts 
    SET likes_count = likes_count - 1 
    WHERE id = OLD.post_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update likes count
CREATE TRIGGER update_post_likes_count_trigger
  AFTER INSERT OR DELETE ON public.post_likes
  FOR EACH ROW
  EXECUTE FUNCTION public.update_post_likes_count();

-- Add helpful comments
COMMENT ON TABLE public.posts IS 'Social posts for the sharing feature';
COMMENT ON COLUMN public.posts.user_name IS 'Name of the user who created the post';
COMMENT ON COLUMN public.posts.avatar IS 'Optional avatar URL for the user';
COMMENT ON COLUMN public.posts.content IS 'Text content of the post';
COMMENT ON COLUMN public.posts.image IS 'Optional image URL attached to the post';
COMMENT ON COLUMN public.posts.likes_count IS 'Number of likes for this post';

COMMENT ON TABLE public.post_comments IS 'Comments on social posts';
COMMENT ON COLUMN public.post_comments.post_id IS 'Reference to the post this comment belongs to';
COMMENT ON COLUMN public.post_comments.user_name IS 'Name of the user who made the comment';
COMMENT ON COLUMN public.post_comments.content IS 'Text content of the comment';

COMMENT ON TABLE public.post_likes IS 'Likes on social posts';
COMMENT ON COLUMN public.post_likes.post_id IS 'Reference to the post that was liked';
COMMENT ON COLUMN public.post_likes.user_name IS 'Name of the user who liked the post';
