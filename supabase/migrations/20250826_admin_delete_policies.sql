-- Update post and comment policies to restrict delete operations to admin only
-- This migration updates the RLS policies for posts and comments tables

-- Drop existing delete policies if they exist
DROP POLICY IF EXISTS "Enable delete for all users" ON public.posts;
DROP POLICY IF EXISTS "Only admins can delete posts" ON public.posts;
DROP POLICY IF EXISTS "Enable delete for all users" ON public.post_comments;
DROP POLICY IF EXISTS "Only admins can delete comments" ON public.post_comments;

-- Create admin-only delete policies for posts
CREATE POLICY "Only admins can delete posts" ON public.posts
  FOR DELETE TO authenticated
  USING (public.get_user_role(auth.uid()) = 'admin');

-- Create admin-only delete policies for comments
CREATE POLICY "Only admins can delete comments" ON public.post_comments
  FOR DELETE TO authenticated
  USING (public.get_user_role(auth.uid()) = 'admin');

-- Note: Likes can still be deleted by all users (for unlike functionality)
-- The existing likes policies remain unchanged

-- Add comments to explain the security model
COMMENT ON POLICY "Only admins can delete posts" ON public.posts IS 'Restricts post deletion to admin users only for content moderation';
COMMENT ON POLICY "Only admins can delete comments" ON public.post_comments IS 'Restricts comment deletion to admin users only for content moderation';
