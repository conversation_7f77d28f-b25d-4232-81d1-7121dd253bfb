-- Add category column for expense categorization (food, family, shopping, etc.)
ALTER TABLE public.transactions 
ADD COLUMN category TEXT;

-- Make note field nullable since users might not always want to add notes
ALTER TABLE public.transactions 
ALTER COLUMN note DROP NOT NULL;

-- Add index for category to improve query performance when filtering by category
CREATE INDEX idx_transactions_category ON public.transactions(category);

-- Add some example categories as comments for future reference
COMMENT ON COLUMN public.transactions.category IS 'Expense category for analysis (e.g., food, family, shopping, transport, entertainment, etc.)';
COMMENT ON COLUMN public.transactions.note IS 'Optional note/description for the transaction';