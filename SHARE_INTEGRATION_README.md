# Share Feature Database Integration

This document outlines the completed integration of the Share page with Supabase database, replacing the hardcoded data with real database operations.

## What Was Implemented

### 1. Database Schema Migration
- **Posts Table**: Stores user posts with content, images, likes count, and metadata
- **Comments Table**: Stores comments on posts with foreign key relationship
- **Likes Table**: Tracks individual likes for future user-specific functionality
- **Proper indexing** for performance optimization
- **Row Level Security (RLS)** enabled with public access policies
- **Automatic triggers** for timestamp updates and likes count management

### 2. TypeScript Types
Updated `src/integrations/supabase/types.ts` with:
- `posts` table type definitions
- `post_comments` table type definitions  
- `post_likes` table type definitions
- Proper relationships and constraints

### 3. Database Operations Hook
Created `src/hooks/usePosts.ts` with:
- `usePosts()` hook providing CRUD operations
- `fetchPosts()` - Load posts with comments
- `createPost()` - Create new posts
- `addComment()` - Add comments to posts
- `likePost()` - Toggle likes on posts
- Loading states and error handling

### 4. Updated Share Component
Modified `src/pages/Share.tsx` to:
- Use `usePosts` hook instead of hardcoded data
- Implement loading states with spinners
- Add error handling and display
- Update UI to use database field names (`user_name`, `likes_count`, `created_at`)
- Add proper async handling for database operations

## Database Setup Instructions

### Option 1: Manual Setup (Recommended)
1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `database_setup.sql`
4. Execute the SQL script

### Option 2: Using Migration Files
If you have Supabase CLI configured:
```bash
npx supabase migration up
```

## Features Implemented

### ✅ Create Posts
- Users can create new posts with text content
- Optional image upload support
- Real-time addition to the posts feed
- Loading states during creation

### ✅ View Posts Feed
- Posts loaded from database in reverse chronological order
- Displays user name, content, images, like counts, and comments
- Proper date formatting in Vietnamese locale
- Loading spinner during initial fetch

### ✅ Add Comments
- Users can comment on any post
- Real-time comment addition
- Loading states for individual comments
- Comments display with timestamps

### ✅ Like Posts
- Users can like/unlike posts
- Automatic like count updates
- Prevents duplicate likes from same user
- Real-time UI updates

### ✅ Error Handling
- Database connection errors
- Failed post creation
- Failed comment addition
- User-friendly error messages

### ✅ Loading States
- Initial page load spinner
- Post creation loading
- Comment submission loading
- Individual operation feedback

## Database Schema Details

### Posts Table
```sql
posts (
  id: UUID PRIMARY KEY
  user_name: TEXT NOT NULL
  avatar: TEXT (optional)
  content: TEXT NOT NULL
  image: TEXT (optional)
  likes_count: INTEGER DEFAULT 0
  created_at: TIMESTAMP WITH TIME ZONE
  updated_at: TIMESTAMP WITH TIME ZONE
)
```

### Comments Table
```sql
post_comments (
  id: UUID PRIMARY KEY
  post_id: UUID REFERENCES posts(id)
  user_name: TEXT NOT NULL
  content: TEXT NOT NULL
  created_at: TIMESTAMP WITH TIME ZONE
  updated_at: TIMESTAMP WITH TIME ZONE
)
```

### Likes Table
```sql
post_likes (
  id: UUID PRIMARY KEY
  post_id: UUID REFERENCES posts(id)
  user_name: TEXT NOT NULL
  created_at: TIMESTAMP WITH TIME ZONE
  UNIQUE(post_id, user_name)
)
```

## Testing the Implementation

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Navigate to the Share page** in your application

3. **Test creating a post**:
   - Enter content in the text area
   - Optionally upload an image
   - Click "Đăng bài"
   - Verify the post appears at the top of the feed

4. **Test commenting**:
   - Click in the comment input under any post
   - Type a comment and press Enter or click "Gửi"
   - Verify the comment appears immediately

5. **Test liking**:
   - Click the heart icon on any post
   - Verify the like count increases
   - Click again to unlike (decreases count)

## Future Enhancements

- **User Authentication**: Integrate with Supabase Auth for user-specific features
- **Real-time Updates**: Add real-time subscriptions for live updates
- **Image Upload**: Implement proper image storage with Supabase Storage
- **Rich Text**: Add support for formatted text and mentions
- **Post Editing**: Allow users to edit their own posts
- **Moderation**: Add content moderation features
- **Pagination**: Implement infinite scroll or pagination for large datasets

## File Structure

```
src/
├── hooks/
│   └── usePosts.ts              # Database operations hook
├── integrations/
│   └── supabase/
│       ├── client.ts            # Supabase client
│       └── types.ts             # Updated TypeScript types
└── pages/
    └── Share.tsx                # Updated Share component

supabase/
└── migrations/
    ├── 20250826_create_share_posts_and_comments.sql
    └── 20250826_insert_sample_share_data.sql

database_setup.sql               # Complete manual setup script
```

## Notes

- The implementation maintains the same UI/UX behavior as the original hardcoded version
- All database operations include proper error handling
- The code is type-safe with full TypeScript support
- Sample data is provided to demonstrate the functionality
- The implementation is production-ready with proper security policies
