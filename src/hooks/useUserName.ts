import { useCallback, useEffect, useMemo, useState } from "react";
import { useAuth } from "@/hooks/useAuth";

const LOCAL_KEY = "guest_user_name";

function normalizeName(name: string | null | undefined): string | null {
  const n = (name ?? "").trim();
  return n.length ? n : null;
}

function deriveNameFromAuth(userEmail?: string | null, userMeta?: Record<string, any> | null): string | null {
  // Prefer a user profile/meta name if available
  const fullName = normalizeName(userMeta?.full_name || userMeta?.name);
  if (fullName) return fullName;

  // Fallback to the email local-part as a readable name
  if (userEmail) {
    const local = userEmail.split("@")[0];
    return local || null;
  }
  return null;
}

export function useUserName() {
  const { user, profile, isAuthenticated } = useAuth();
  // Initialize synchronously so first render has value and avoids premature prompt
  const [guestName, setGuestName] = useState<string | null>(() => {
    try {
      const stored = typeof window !== 'undefined' ? localStorage.getItem(LOCAL_KEY) : null;
      return normalizeName(stored);
    } catch {
      return null;
    }
  });

  const authName = useMemo(() => {
    if (!isAuthenticated) return null;
    return (
      deriveNameFromAuth(user?.email ?? null, (user as any)?.user_metadata ?? null) ||
      // If profile contains a name in your schema, prefer it (extend when available)
      (profile as any)?.name ||
      null
    );
  }, [isAuthenticated, user, profile]);

  const name = useMemo(() => {
    return authName ?? guestName;
  }, [authName, guestName]);

  const promptForName = useCallback(() => {
    const input = window.prompt("Please enter your name to personalize your experience:", guestName ?? "");
    const normalized = normalizeName(input);
    if (normalized) {
      localStorage.setItem(LOCAL_KEY, normalized);
      setGuestName(normalized);
      return normalized;
    }
    return null;
  }, [guestName]);

  const ensureName = useCallback(() => {
    if (isAuthenticated) return authName ?? null;
    if (guestName) return guestName;
    return promptForName();
  }, [isAuthenticated, authName, guestName, promptForName]);

  const setName = useCallback((newName: string) => {
    const normalized = normalizeName(newName);
    if (!normalized) return;
    // Only stored locally for guests. For authenticated users, updating should go to backend.
    if (!isAuthenticated) {
      localStorage.setItem(LOCAL_KEY, normalized);
      setGuestName(normalized);
    }
  }, [isAuthenticated]);

  return {
    name,
    isAuthenticated,
    ensureName,
    promptForName,
    setName,
    source: isAuthenticated ? "auth" as const : "local" as const,
  };
}

export const __private__ = { normalizeName, deriveNameFromAuth };
