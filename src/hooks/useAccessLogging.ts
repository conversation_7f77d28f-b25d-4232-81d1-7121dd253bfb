import { useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useUserName } from './useUserName';
import { useToast } from './use-toast';

export const useAccessLogging = () => {
  const { user, isAuthenticated, loading, profile, isAdmin } = useAuth();
  const { name: userName, ensureName } = useUserName();
  const { toast } = useToast();
  const hasLoggedRef = useRef(false);
  const currentSessionRef = useRef<string | null>(null);

  const logAccess = async (email?: string) => {
    try {
      // Get current page information
      const currentPage = window.location.pathname;
      const currentUrl = window.location.href;
      
      // Determine email based on authentication status
      let emailToLog: string | null = null;
      
      if (isAuthenticated && user) {
        // For authenticated users, use their email from the user object
        emailToLog = user.email || null;
      } else {
        // For guest users, check localStorage first, then use provided email
        const guestEmail = localStorage.getItem('guest_user_name');
        emailToLog = guestEmail || email || null;
      }
      
      const logData = {
        user_id: isAuthenticated ? user?.id : null,
        email: emailToLog,
        timestamp: new Date().toISOString(),
      };

      console.log('Logging access:', {
        isAuthenticated,
        userId: user?.id,
        userRole: isAdmin ? 'admin' : 'user',
        page: currentPage,
        email: emailToLog,
        loadingState: loading
      });

      const { data, error } = await supabase
        .from('user_logs')
        .insert([logData])
        .select();

      if (error) {
        console.error('Error logging access:', error);
        // Only show toast for critical errors in development
        if (process.env.NODE_ENV === 'development') {
          toast({
            title: "Access Logging Warning",
            description: `Failed to log access: ${error.message}`,
            variant: "destructive",
          });
        }
      } else {
        console.log('Access logged successfully:', data);
      }
    } catch (error) {
      console.error('Unexpected error logging access:', error);
    }
  };

  const handleAuthenticatedAccess = async () => {
    if (!user?.id) {
      console.warn('Authenticated user missing ID, skipping log');
      return;
    }

    // Prevent duplicate logging for the same session and page
    const sessionKey = `${user.id}_${window.location.pathname}`;
    if (currentSessionRef.current === sessionKey) {
      console.log('Already logged for this session and page, skipping duplicate');
      return;
    }

    console.log('Logging authenticated user access:', {
      userId: user.id,
      email: user.email,
      isAdmin,
      profileRole: profile?.role,
      page: window.location.pathname
    });

    await logAccess();
    currentSessionRef.current = sessionKey;
  };

  const handleGuestAccess = async () => {
    // Prevent duplicate guest logging for the same page in this session
    const guestSessionKey = `guest_${window.location.pathname}`;
    if (currentSessionRef.current === guestSessionKey) {
      console.log('Already logged guest access for this page, skipping duplicate');
      return;
    }

    // Check localStorage for guest email first
    const guestEmail = localStorage.getItem('guest_user_name');
    
    if (guestEmail) {
      console.log('Using guest email from localStorage:', guestEmail);
      await logAccess(guestEmail);
    } else {
      // Fallback: use the unified user name management for compatibility
      const guestName = await ensureName();
      if (guestName) {
        console.log('Using guest name from useUserName hook as email:', guestName);
        await logAccess(guestName);
      } else {
        console.log('No guest email/name provided, logging as Anonymous Guest');
        await logAccess('Anonymous Guest');
      }
    }
    
    currentSessionRef.current = guestSessionKey;
  };

  useEffect(() => {
    // Don't log while auth is still loading
    if (loading) {
      console.log('Auth still loading, skipping access log');
      return;
    }

    // Prevent duplicate logging on re-renders
    if (hasLoggedRef.current) {
      console.log('Already logged access for this component mount, skipping');
      return;
    }

    console.log('useAccessLogging effect triggered:', {
      isAuthenticated,
      userId: user?.id,
      loading,
      hasLogged: hasLoggedRef.current
    });

    // Log access on component mount (page load)
    if (isAuthenticated && user?.id) {
      handleAuthenticatedAccess();
    } else if (!isAuthenticated) {
      handleGuestAccess();
    }

    hasLoggedRef.current = true;

    // Reset the flag when user changes or page changes
    return () => {
      hasLoggedRef.current = false;
    };
  }, [isAuthenticated, user?.id, loading]);

  // Reset logging flag when navigating to a new page
  useEffect(() => {
    const handleLocationChange = () => {
      console.log('Page changed, resetting access logging flag');
      hasLoggedRef.current = false;
      currentSessionRef.current = null;
    };

    // Listen for navigation changes
    window.addEventListener('popstate', handleLocationChange);
    
    return () => {
      window.removeEventListener('popstate', handleLocationChange);
    };
  }, []);

  return {
    logAccess,
    handleGuestAccess,
    handleAuthenticatedAccess,
  };
};