import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useToast } from './use-toast';

export const useAccessLogging = () => {
  const { user, isAuthenticated } = useAuth();

  const logAccess = async (guestName?: string) => {
    try {
      const logData = {
        user_id: isAuthenticated ? user?.id : null,
        guest_name: !isAuthenticated ? guestName : null,
        timestamp: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('user_logs')
        .insert([logData]);

      if (error) {
        console.error('Error logging access:', error);
      }
    } catch (error) {
      console.error('Unexpected error logging access:', error);
    }
  };

  const handleGuestAccess = async () => {
    const existingGuestName = localStorage.getItem('guestUsername');
    
    if (existingGuestName) {
      await logAccess(existingGuestName);
    } else {
      // Use a simple prompt for guest name
      const guestName = prompt('Welcome! Please enter a display name for your session:');
      
      if (guestName?.trim()) {
        localStorage.setItem('guestUsername', guestName.trim());
        await logAccess(guestName.trim());
      } else {
        await logAccess('Anonymous Guest');
      }
    }
  };

  useEffect(() => {
    // Log access on component mount (page load)
    if (isAuthenticated) {
      logAccess();
    } else {
      handleGuestAccess();
    }
  }, [isAuthenticated, user?.id]);

  return {
    logAccess,
    handleGuestAccess,
  };
};
