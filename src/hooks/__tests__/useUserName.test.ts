import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { __private__ } from "../useUserName";

const { normalizeName, deriveNameFromAuth } = __private__;

describe("useUserName utils", () => {
  describe("normalizeName", () => {
    it("returns null for empty or whitespace", () => {
      expect(normalizeName("")).toBeNull();
      expect(normalizeName("   ")).toBeNull();
      expect(normalizeName(undefined)).toBeNull();
      expect(normalizeName(null)).toBeNull();
    });

    it("trims valid names", () => {
      expect(normalizeName("  Alice  ")).toBe("Alice");
    });
  });

  describe("deriveNameFromAuth", () => {
    it("prefers full_name from metadata", () => {
      expect(deriveNameFromAuth("<EMAIL>", { full_name: "<PERSON> Doe" })).toBe("Alice Doe");
    });

    it("falls back to email local part", () => {
      expect(deriveNameFromAuth("<EMAIL>", {})).toBe("user.name");
    });

    it("returns null if nothing available", () => {
      expect(deriveNameFromAuth(undefined, null)).toBeNull();
    });
  });
});
