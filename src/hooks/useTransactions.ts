import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Transaction {
  id: string;
  type: "money-in" | "money-out";
  amount: number;
  note: string | null;
  category: string | null;
  date: Date;
  balance: number;
  created_at?: Date;
  updated_at?: Date;
}

export function useTransactions() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [balance, setBalance] = useState(0);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Fetch transactions from Supabase
  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .order('date', { ascending: false });

      if (error) {
        console.error('Error fetching transactions:', error);
        toast({
          title: "Error",
          description: "Failed to load transactions",
          variant: "destructive",
        });
        return;
      }

      // Convert data to Transaction format
      const formattedTransactions: Transaction[] = (data || []).map(item => ({
        ...item,
        type: item.type as "money-in" | "money-out",
        date: new Date(item.date),
        created_at: item.created_at ? new Date(item.created_at) : undefined,
        updated_at: item.updated_at ? new Date(item.updated_at) : undefined,
      }));

      setTransactions(formattedTransactions);

      // Calculate current balance from latest transaction
      if (formattedTransactions.length > 0) {
        const latestTransaction = formattedTransactions.reduce((latest, current) => 
          new Date(current.date) > new Date(latest.date) ? current : latest
        );
        setBalance(latestTransaction.balance);
      } else {
        setBalance(0);
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Recalculate balances for all transactions and persist to DB
  const recalculateAllBalances = async () => {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .order('date', { ascending: true });

      if (error) throw error;

      let running = 0;
      const toUpdate: { id: string; balance: number }[] = [];
      (data || []).forEach((item) => {
        const type = item.type as "money-in" | "money-out";
        running = type === 'money-in' ? running + item.amount : running - item.amount;
        if (item.balance !== running) {
          toUpdate.push({ id: item.id, balance: running });
        }
      });

      if (toUpdate.length > 0) {
        // Update balances. Perform in parallel in small batches
        await Promise.all(
          toUpdate.map((u) =>
            supabase.from('transactions').update({ balance: u.balance }).eq('id', u.id)
          )
        );
      }

      await fetchTransactions();
    } catch (err) {
      console.error('Error recalculating balances:', err);
      toast({
        title: 'Error',
        description: 'Failed to recalculate balances',
        variant: 'destructive',
      });
    }
  };

  // Add new transaction
  const addTransaction = async (newTransaction: Omit<Transaction, "id" | "balance" | "created_at" | "updated_at">) => {
    try {
      const newBalance = newTransaction.type === "money-in" 
        ? balance + newTransaction.amount 
        : balance - newTransaction.amount;

      const { data, error } = await supabase
        .from('transactions')
        .insert([{
          type: newTransaction.type,
          amount: newTransaction.amount,
          note: newTransaction.note || null,
          category: newTransaction.category || null,
          date: newTransaction.date.toISOString(),
          balance: newBalance,
        }])
        .select()
        .single();

      if (error) {
        console.error('Error adding transaction:', error);
        toast({
          title: "Error",
          description: "Failed to add transaction",
          variant: "destructive",
        });
        return;
      }

      // Format the new transaction and add to state
      const formattedTransaction: Transaction = {
        ...data,
        type: data.type as "money-in" | "money-out",
        date: new Date(data.date),
        created_at: new Date(data.created_at),
        updated_at: new Date(data.updated_at),
      };

      setTransactions(prev => [formattedTransaction, ...prev]);
      setBalance(newBalance);

      toast({
        title: "Success",
        description: "Transaction added successfully",
      });
    } catch (error) {
      console.error('Unexpected error:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  // Update transaction
  const updateTransaction = async (
    id: string,
    updates: Partial<Pick<Transaction, "amount" | "date" | "category" | "note">>
  ) => {
    try {
      const payload: Record<string, any> = {};
      if (typeof updates.amount === 'number') payload.amount = updates.amount;
      if (updates.date instanceof Date) payload.date = updates.date.toISOString();
      if (updates.category !== undefined) payload.category = updates.category ?? null;
      if (updates.note !== undefined) payload.note = updates.note ?? null;

      const { error } = await supabase
        .from('transactions')
        .update({
          ...payload,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);

      if (error) {
        console.error('Error updating transaction:', error);
        toast({
          title: "Error",
          description: "Failed to update transaction",
          variant: "destructive",
        });
        return false;
      }

      // Recalculate balances after any update as amount/date may affect ordering
      await recalculateAllBalances();

      toast({
        title: "Success",
        description: "Transaction updated successfully",
      });
      return true;
    } catch (error) {
      console.error('Unexpected error:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
      return false;
    }
  };

  // Delete transaction
  const deleteTransaction = async (id: string) => {
    try {
      const { error } = await supabase
        .from('transactions')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting transaction:', error);
        toast({
          title: "Error",
          description: "Failed to delete transaction",
          variant: "destructive",
        });
        return;
      }

      // Recalculate balances after deletion
      await recalculateAllBalances();

      toast({
        title: "Success",
        description: "Transaction deleted successfully",
      });
    } catch (error) {
      console.error('Unexpected error:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  // Initial load
  useEffect(() => {
    fetchTransactions();
  }, []);

  return {
    transactions,
    balance,
    loading,
    addTransaction,
    updateTransaction,
    deleteTransaction,
    refetch: fetchTransactions,
  };
}