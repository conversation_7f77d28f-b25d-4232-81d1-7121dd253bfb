import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';

export interface UserData {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
  lastLogin: string;
  status: 'Online' | 'Offline';
  isGuest: boolean;
}

export const useUsers = () => {
  const [users, setUsers] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user: currentUser } = useAuth();

  // Mock data for demonstration
  const getMockUsers = (): UserData[] => {
    const guestName = localStorage.getItem('guest_user_name') || 'Anonymous Guest';
    
    return [
      {
        id: 'mock-admin-1',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        lastLogin: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        status: 'Online',
        isGuest: false,
      },
      {
        id: 'mock-user-1',
        name: '<PERSON>',
        email: '<EMAIL>',
        role: 'user',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        status: 'Offline',
        isGuest: false,
      },
      {
        id: 'mock-user-2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        role: 'user',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
        status: 'Offline',
        isGuest: false,
      },
      {
        id: 'mock-user-3',
        name: 'Bob Johnson',
        email: '<EMAIL>',
        role: 'user',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(), // 3 days ago
        status: 'Offline',
        isGuest: false,
      },
      {
        id: 'mock-guest-1',
        name: 'Sarah Wilson',
        email: 'Guest',
        role: 'user',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
        status: 'Offline',
        isGuest: true,
      },
      {
        id: 'mock-guest-2',
        name: 'Mike Davis',
        email: 'Guest',
        role: 'user',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString(), // 12 hours ago
        status: 'Offline',
        isGuest: true,
      },
      {
        id: 'current-guest',
        name: guestName,
        email: 'Guest',
        role: 'user',
        lastLogin: new Date().toISOString(),
        status: currentUser ? 'Offline' : 'Online',
        isGuest: true,
      },
    ];
  };

  const fetchUsersFromSupabase = async () => {
    try {
      // Try to fetch real users from Supabase profiles
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (profilesError) {
        console.error('Error fetching profiles:', profilesError);
        return null;
      }

      // Try to fetch user logs to get guest users
      const { data: userLogs, error: logsError } = await supabase
        .from('user_logs')
        .select('*')
        .order('timestamp', { ascending: false });

      if (logsError) {
        console.error('Error fetching user logs:', logsError);
        return null;
      }

      // Process the data to create UserData array
      const userData: UserData[] = [];

      // Add registered users
      if (profiles) {
        profiles.forEach(profile => {
          userData.push({
            id: profile.id,
            name: profile.email?.split('@')[0] || 'Unknown User',
            email: profile.email || '',
            role: profile.role,
            lastLogin: profile.updated_at,
            status: currentUser?.id === profile.id ? 'Online' : 'Offline',
            isGuest: false,
          });
        });
      }

      // Add guest users from logs
      if (userLogs) {
        const guestNames = new Set<string>();
        userLogs.forEach(log => {
          if (log.guest_name && !guestNames.has(log.guest_name)) {
            guestNames.add(log.guest_name);
            userData.push({
              id: `guest-${log.guest_name}`,
              name: log.guest_name,
              email: 'Guest',
              role: 'user',
              lastLogin: log.timestamp,
              status: log.guest_name === localStorage.getItem('guest_user_name') && !currentUser ? 'Online' : 'Offline',
              isGuest: true,
            });
          }
        });
      }

      return userData;
    } catch (error) {
      console.error('Unexpected error fetching users:', error);
      return null;
    }
  };

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);

    try {
      // Try to fetch real data first
      const realUsers = await fetchUsersFromSupabase();
      
      if (realUsers && realUsers.length > 0) {
        setUsers(realUsers);
      } else {
        // Fallback to mock data
        setUsers(getMockUsers());
      }
    } catch (err) {
      console.error('Error in fetchUsers:', err);
      setError('Failed to fetch users');
      // Fallback to mock data on error
      setUsers(getMockUsers());
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [currentUser]);

  return {
    users,
    loading,
    error,
    refetchUsers: fetchUsers,
  };
};
