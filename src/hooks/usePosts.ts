import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { Tables, TablesInsert } from '@/integrations/supabase/types';

export type Post = Tables<'posts'> & {
  comments: Tables<'post_comments'>[];
};

export type Comment = Tables<'post_comments'>;
export type PostInsert = TablesInsert<'posts'>;
export type CommentInsert = TablesInsert<'post_comments'>;

export const usePosts = () => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all posts with their comments
  const fetchPosts = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch posts
      const { data: postsData, error: postsError } = await supabase
        .from('posts')
        .select('*')
        .order('created_at', { ascending: false });

      if (postsError) {
        console.error('Error fetching posts:', postsError);
        setError('Failed to fetch posts');
        return;
      }

      // Fetch comments for all posts
      const { data: commentsData, error: commentsError } = await supabase
        .from('post_comments')
        .select('*')
        .order('created_at', { ascending: true });

      if (commentsError) {
        console.error('Error fetching comments:', commentsError);
        setError('Failed to fetch comments');
        return;
      }

      // Group comments by post_id
      const commentsByPost = commentsData.reduce((acc, comment) => {
        if (!acc[comment.post_id]) {
          acc[comment.post_id] = [];
        }
        acc[comment.post_id].push(comment);
        return acc;
      }, {} as Record<string, Comment[]>);

      // Combine posts with their comments
      const postsWithComments: Post[] = postsData.map(post => ({
        ...post,
        comments: commentsByPost[post.id] || []
      }));

      setPosts(postsWithComments);
    } catch (err) {
      console.error('Unexpected error:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Create a new post
  const createPost = async (postData: Omit<PostInsert, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      setError(null);
      
      const { data, error } = await supabase
        .from('posts')
        .insert(postData)
        .select()
        .single();

      if (error) {
        console.error('Error creating post:', error);
        setError('Failed to create post');
        return null;
      }

      // Add the new post to the local state
      const newPost: Post = {
        ...data,
        comments: []
      };
      
      setPosts(prevPosts => [newPost, ...prevPosts]);
      return data;
    } catch (err) {
      console.error('Unexpected error creating post:', err);
      setError('An unexpected error occurred while creating post');
      return null;
    }
  };

  // Add a comment to a post
  const addComment = async (commentData: Omit<CommentInsert, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      setError(null);
      
      const { data, error } = await supabase
        .from('post_comments')
        .insert(commentData)
        .select()
        .single();

      if (error) {
        console.error('Error adding comment:', error);
        setError('Failed to add comment');
        return null;
      }

      // Add the comment to the local state
      setPosts(prevPosts => 
        prevPosts.map(post => 
          post.id === commentData.post_id 
            ? { ...post, comments: [...post.comments, data] }
            : post
        )
      );
      
      return data;
    } catch (err) {
      console.error('Unexpected error adding comment:', err);
      setError('An unexpected error occurred while adding comment');
      return null;
    }
  };

  // Like a post (simplified - just increment the count)
  const likePost = async (postId: string, userName: string) => {
    try {
      setError(null);
      
      // Check if user has already liked this post
      const { data: existingLike, error: checkError } = await supabase
        .from('post_likes')
        .select('id')
        .eq('post_id', postId)
        .eq('user_name', userName)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error('Error checking existing like:', checkError);
        setError('Failed to check like status');
        return null;
      }

      if (existingLike) {
        // User has already liked this post, so unlike it
        const { error: deleteError } = await supabase
          .from('post_likes')
          .delete()
          .eq('id', existingLike.id);

        if (deleteError) {
          console.error('Error unliking post:', deleteError);
          setError('Failed to unlike post');
          return null;
        }

        // Update local state
        setPosts(prevPosts => 
          prevPosts.map(post => 
            post.id === postId 
              ? { ...post, likes_count: Math.max(0, post.likes_count - 1) }
              : post
          )
        );
        
        return { action: 'unliked' };
      } else {
        // User hasn't liked this post yet, so like it
        const { data, error: insertError } = await supabase
          .from('post_likes')
          .insert({ post_id: postId, user_name: userName })
          .select()
          .single();

        if (insertError) {
          console.error('Error liking post:', insertError);
          setError('Failed to like post');
          return null;
        }

        // Update local state
        setPosts(prevPosts => 
          prevPosts.map(post => 
            post.id === postId 
              ? { ...post, likes_count: post.likes_count + 1 }
              : post
          )
        );
        
        return { action: 'liked', data };
      }
    } catch (err) {
      console.error('Unexpected error liking post:', err);
      setError('An unexpected error occurred while liking post');
      return null;
    }
  };

  // Get user's like status for a post
  const getUserLikeStatus = async (postId: string, userName: string) => {
    try {
      const { data, error } = await supabase
        .from('post_likes')
        .select('id')
        .eq('post_id', postId)
        .eq('user_name', userName)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error checking like status:', error);
        return false;
      }

      return !!data;
    } catch (err) {
      console.error('Unexpected error checking like status:', err);
      return false;
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchPosts();
  }, []);

  return {
    posts,
    loading,
    error,
    createPost,
    addComment,
    likePost,
    getUserLikeStatus,
    refreshPosts: fetchPosts
  };
};
