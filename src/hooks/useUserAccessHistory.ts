import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface AccessHistoryRecord {
  id: string;
  timestamp: string;
  action: string;
  page?: string;
  location?: string;
  ipAddress?: string;
  userAgent?: string;
}

export const useUserAccessHistory = (userId: string) => {
  const [accessHistory, setAccessHistory] = useState<AccessHistoryRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data for demonstration
  const getMockAccessHistory = (userId: string): AccessHistoryRecord[] => {
    const baseData = [
      {
        id: `${userId}-access-1`,
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        action: 'page_visit',
        page: '/users',
        location: 'Users Page',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      {
        id: `${userId}-access-2`,
        timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago
        action: 'page_visit',
        page: '/reports',
        location: 'Reports Page',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      {
        id: `${userId}-access-3`,
        timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
        action: 'login',
        page: '/auth',
        location: 'Authentication',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      {
        id: `${userId}-access-4`,
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        action: 'page_visit',
        page: '/',
        location: 'Dashboard',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      {
        id: `${userId}-access-5`,
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
        action: 'logout',
        page: '/',
        location: 'Dashboard',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      {
        id: `${userId}-access-6`,
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
        action: 'login',
        page: '/auth',
        location: 'Authentication',
        ipAddress: '************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      },
      {
        id: `${userId}-access-7`,
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(), // 2 days ago
        action: 'page_visit',
        page: '/shares',
        location: 'Share Page',
        ipAddress: '************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      },
    ];

    // Add some variation for guest users
    if (userId.startsWith('guest-') || userId === 'current-guest') {
      return baseData.map(record => ({
        ...record,
        action: record.action === 'login' || record.action === 'logout' 
          ? 'page_visit' 
          : record.action,
      }));
    }

    return baseData;
  };

  const fetchAccessHistoryFromSupabase = async (userId: string): Promise<AccessHistoryRecord[]> => {
    try {
      // Check if it's a guest user
      if (userId.startsWith('guest-') || userId === 'current-guest') {
        const guestName = userId === 'current-guest' 
          ? localStorage.getItem('guest_user_name') || 'Anonymous Guest'
          : userId.replace('guest-', '');

        // Fetch guest access logs
        const { data: guestLogs, error: logsError } = await supabase
          .from('user_logs')
          .select('*')
          .eq('email', guestName)
          .order('timestamp', { ascending: false })
          .limit(50);

        if (logsError) {
          console.error('Error fetching guest access history:', logsError);
          return [];
        }

        if (guestLogs) {
          return guestLogs.map(log => ({
            id: log.id,
            timestamp: log.timestamp,
            action: 'page_visit',
            page: 'Unknown',
            location: 'Page Visit',
            ipAddress: 'N/A',
            userAgent: 'N/A',
          }));
        }
      } else {
        // For registered users, fetch from user_logs
        const { data: userLogs, error: logsError } = await supabase
          .from('user_logs')
          .select('*')
          .eq('user_id', userId)
          .order('timestamp', { ascending: false })
          .limit(50);

        if (logsError) {
          console.error('Error fetching user access history:', logsError);
          return [];
        }

        if (userLogs) {
          return userLogs.map(log => ({
            id: log.id,
            timestamp: log.timestamp,
            action: 'page_visit',
            page: 'Unknown',
            location: 'Page Visit',
            ipAddress: 'N/A',
            userAgent: 'N/A',
          }));
        }
      }

      return [];
    } catch (error) {
      console.error('Unexpected error fetching access history:', error);
      return [];
    }
  };

  const fetchAccessHistory = async () => {
    if (!userId) {
      setError('User ID is required');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Try to fetch real data first
      const realHistory = await fetchAccessHistoryFromSupabase(userId);
      
      if (realHistory.length > 0) {
        setAccessHistory(realHistory);
      } else {
        // Fallback to mock data
        const mockHistory = getMockAccessHistory(userId);
        setAccessHistory(mockHistory);
      }
    } catch (err) {
      console.error('Error in fetchAccessHistory:', err);
      setError('Failed to fetch access history');
      
      // Fallback to mock data on error
      const mockHistory = getMockAccessHistory(userId);
      setAccessHistory(mockHistory);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAccessHistory();
  }, [userId]);

  return {
    accessHistory,
    loading,
    error,
    refetchHistory: fetchAccessHistory,
  };
};
