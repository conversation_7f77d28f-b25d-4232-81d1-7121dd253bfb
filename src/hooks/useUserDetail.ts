import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { UserData } from './useUsers';

export const useUserDetail = (userId: string) => {
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data for demonstration (same as useUsers)
  const getMockUsers = (): UserData[] => {
    const guestName = localStorage.getItem('guest_user_name') || 'Anonymous Guest';
    
    return [
      {
        id: 'mock-admin-1',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        lastLogin: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        status: 'Online',
        isGuest: false,
      },
      {
        id: 'mock-user-1',
        name: '<PERSON>',
        email: '<EMAIL>',
        role: 'user',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        status: 'Offline',
        isGuest: false,
      },
      {
        id: 'mock-user-2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        role: 'user',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
        status: 'Offline',
        isGuest: false,
      },
      {
        id: 'mock-user-3',
        name: 'Bob Johnson',
        email: '<EMAIL>',
        role: 'user',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(), // 3 days ago
        status: 'Offline',
        isGuest: false,
      },
      {
        id: 'mock-guest-1',
        name: 'Sarah Wilson',
        email: 'Guest',
        role: 'user',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
        status: 'Offline',
        isGuest: true,
      },
      {
        id: 'mock-guest-2',
        name: 'Mike Davis',
        email: 'Guest',
        role: 'user',
        lastLogin: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString(), // 12 hours ago
        status: 'Offline',
        isGuest: true,
      },
      {
        id: 'current-guest',
        name: guestName,
        email: 'Guest',
        role: 'user',
        lastLogin: new Date().toISOString(),
        status: 'Online',
        isGuest: true,
      },
    ];
  };

  const fetchUserFromSupabase = async (userId: string): Promise<UserData | null> => {
    try {
      // Check if it's a guest user
      if (userId.startsWith('guest-') || userId === 'current-guest') {
        const guestName = userId === 'current-guest' 
          ? localStorage.getItem('guest_user_name') || 'Anonymous Guest'
          : userId.replace('guest-', '');

        // Try to get guest info from user_logs
        const { data: guestLogs, error: logsError } = await supabase
          .from('user_logs')
          .select('*')
          .eq('guest_name', guestName)
          .order('timestamp', { ascending: false })
          .limit(1);

        if (logsError) {
          console.error('Error fetching guest logs:', logsError);
          return null;
        }

        if (guestLogs && guestLogs.length > 0) {
          const latestLog = guestLogs[0];
          return {
            id: userId,
            name: guestName,
            email: 'Guest',
            role: 'user',
            lastLogin: latestLog.timestamp,
            status: guestName === localStorage.getItem('guest_user_name') ? 'Online' : 'Offline',
            isGuest: true,
          };
        }
        return null;
      }

      // For registered users, fetch from profiles
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error('Error fetching user profile:', profileError);
        return null;
      }

      if (profile) {
        return {
          id: profile.id,
          name: profile.email?.split('@')[0] || 'Unknown User',
          email: profile.email || '',
          role: profile.role,
          lastLogin: profile.updated_at,
          status: 'Offline', // We don't have real-time status tracking yet
          isGuest: false,
        };
      }

      return null;
    } catch (error) {
      console.error('Unexpected error fetching user:', error);
      return null;
    }
  };

  const fetchUser = async () => {
    if (!userId) {
      setError('User ID is required');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Try to fetch real data first
      const realUser = await fetchUserFromSupabase(userId);
      
      if (realUser) {
        setUser(realUser);
      } else {
        // Fallback to mock data
        const mockUsers = getMockUsers();
        const mockUser = mockUsers.find(u => u.id === userId);
        
        if (mockUser) {
          setUser(mockUser);
        } else {
          setError('User not found');
        }
      }
    } catch (err) {
      console.error('Error in fetchUser:', err);
      setError('Failed to fetch user details');
      
      // Try fallback to mock data
      const mockUsers = getMockUsers();
      const mockUser = mockUsers.find(u => u.id === userId);
      if (mockUser) {
        setUser(mockUser);
        setError(null);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUser();
  }, [userId]);

  return {
    user,
    loading,
    error,
    refetchUser: fetchUser,
  };
};
