// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://eustayibwoqhciyrcoqd.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1c3RheWlid29xaGNpeXJjb3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTU5NTcyMjYsImV4cCI6MjA3MTUzMzIyNn0.oCe81cjKbftozpLsw8CAA0HlGe9mxixkMCNUr8KTM3M";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});