/* @vitest-environment node */
import { describe, it, expect } from 'vitest';
import { shouldNavigateToDashboard } from '../Auth';

// Unit tests for the pure navigation decision function
// Ensures we only navigate when authenticated or a guest name is ensured

describe('shouldNavigateToDashboard', () => {
  it('navigates when authenticated regardless of name', () => {
    expect(shouldNavigateToDashboard(null, true)).toBe(true);
    expect(shouldNavigateToDashboard('Alice', true)).toBe(true);
  });

  it('navigates when guest has an ensured name', () => {
    expect(shouldNavigateToDashboard('Bob', false)).toBe(true);
  });

  it('does not navigate when guest has no name', () => {
    expect(shouldNavigateToDashboard(null, false)).toBe(false);
    expect(shouldNavigateToDashboard('', false)).toBe(false);
  });
});
