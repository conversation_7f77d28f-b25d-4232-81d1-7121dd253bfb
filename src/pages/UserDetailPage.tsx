import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, User, Mail, Shield, Clock, Eye, Calendar } from 'lucide-react';
import { AdminRouteGuard } from '@/components/AdminRouteGuard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { useUserDetail } from '@/hooks/useUserDetail';
import { useUserAccessHistory } from '@/hooks/useUserAccessHistory';
import { useAccessLogging } from '@/hooks/useAccessLogging';
import { format } from 'date-fns';

const UserDetailPageContent = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const { user, loading: userLoading, error: userError } = useUserDetail(userId!);
  const { accessHistory, loading: historyLoading, error: historyError, refetchHistory } = useUserAccessHistory(userId!);
  
  // Initialize access logging
  useAccessLogging();

  const handleBackClick = () => {
    navigate('/users');
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getStatusBadge = (status: string) => {
    return status === 'Online' ? (
      <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
        <div className="h-2 w-2 bg-green-500 rounded-full mr-1" />
        Online
      </Badge>
    ) : (
      <Badge variant="secondary">
        <div className="h-2 w-2 bg-gray-400 rounded-full mr-1" />
        Offline
      </Badge>
    );
  };

  const getRoleBadge = (role: string) => {
    return role === 'admin' ? (
      <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">
        <Shield className="h-3 w-3 mr-1" />
        Admin
      </Badge>
    ) : (
      <Badge variant="outline">
        <User className="h-3 w-3 mr-1" />
        User
      </Badge>
    );
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      return format(new Date(timestamp), 'MMM d, yyyy HH:mm:ss');
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getActionBadge = (action: string) => {
    switch (action.toLowerCase()) {
      case 'login':
        return <Badge className="bg-green-100 text-green-800">Login</Badge>;
      case 'logout':
        return <Badge className="bg-red-100 text-red-800">Logout</Badge>;
      case 'page_visit':
        return <Badge className="bg-blue-100 text-blue-800">Page Visit</Badge>;
      default:
        return <Badge variant="secondary">{action}</Badge>;
    }
  };

  if (userLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading user details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (userError || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p>Error loading user details: {userError || 'User not found'}</p>
              <Button 
                onClick={handleBackClick} 
                className="mt-4"
                variant="outline"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Users
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          onClick={handleBackClick}
          variant="outline"
          size="sm"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Users
        </Button>
        <div>
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent">
            User Details
          </h1>
          <p className="text-muted-foreground">
            View user information and access history
          </p>
        </div>
      </div>

      {/* User Information Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-6">
            {/* Avatar Section */}
            <div className="flex-shrink-0">
              <Avatar className="h-24 w-24">
                <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.name}`} />
                <AvatarFallback className="text-lg font-semibold bg-orange-100 text-orange-800">
                  {getInitials(user.name)}
                </AvatarFallback>
              </Avatar>
            </div>

            {/* User Details */}
            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Name</label>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-lg font-semibold">{user.name}</span>
                    {user.isGuest && (
                      <Badge variant="outline" className="text-xs">
                        Guest
                      </Badge>
                    )}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email</label>
                  <div className="flex items-center gap-2 mt-1">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {user.isGuest ? (
                        <span className="text-muted-foreground italic">Guest User</span>
                      ) : (
                        user.email
                      )}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Role</label>
                  <div className="mt-1">
                    {getRoleBadge(user.role)}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <div className="mt-1">
                    {getStatusBadge(user.status)}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last Login</label>
                  <div className="flex items-center gap-2 mt-1">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{formatTimestamp(user.lastLogin)}</span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">User Type</label>
                  <div className="mt-1">
                    <Badge variant={user.isGuest ? "secondary" : "default"}>
                      {user.isGuest ? 'Guest User' : 'Registered User'}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Access History Section */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Access History
          </CardTitle>
          <Button
            onClick={refetchHistory}
            variant="outline"
            size="sm"
            disabled={historyLoading}
          >
            <Calendar className={`h-4 w-4 mr-2 ${historyLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          {historyLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-600"></div>
                <span>Loading access history...</span>
              </div>
            </div>
          ) : historyError ? (
            <div className="text-center text-red-600 py-8">
              <p>Error loading access history: {historyError}</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow className="bg-orange-50">
                    <TableHead className="font-semibold text-orange-900">Timestamp</TableHead>
                    <TableHead className="font-semibold text-orange-900">Action</TableHead>
                    <TableHead className="font-semibold text-orange-900">Page/Location</TableHead>
                    <TableHead className="font-semibold text-orange-900">IP Address</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {accessHistory.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                        No access history found for this user.
                      </TableCell>
                    </TableRow>
                  ) : (
                    accessHistory.map((record, index) => (
                      <TableRow key={index} className="hover:bg-muted/50">
                        <TableCell className="text-muted-foreground">
                          {formatTimestamp(record.timestamp)}
                        </TableCell>
                        <TableCell>
                          {getActionBadge(record.action)}
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {record.page || record.location || 'Unknown'}
                        </TableCell>
                        <TableCell className="font-mono text-sm text-muted-foreground">
                          {record.ipAddress || 'N/A'}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

const UserDetailPage = () => {
  return (
    <AdminRouteGuard>
      <UserDetailPageContent />
    </AdminRouteGuard>
  );
};

export default UserDetailPage;
