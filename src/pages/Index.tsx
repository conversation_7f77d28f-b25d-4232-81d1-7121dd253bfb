import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, BarChart3, Loader2, Users, UserCheck } from "lucide-react";
import { SummaryCards } from "@/components/SummaryCards";
import { TransactionTable } from "@/components/TransactionTable";
import { TransactionModal } from "@/components/TransactionModal";
import { UserBadge } from "@/components/UserBadge";
import { useTransactions } from "@/hooks/useTransactions";
import { useAuth } from "@/hooks/useAuth";
import { useAccessLogging } from "@/hooks/useAccessLogging";

const Index = () => {
  const navigate = useNavigate();
  const { isAdmin } = useAuth();
  const { transactions, balance, loading, addTransaction, updateTransaction, deleteTransaction } = useTransactions();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Initialize access logging
  useAccessLogging();

  const handleAddTransaction = async (
    newTransaction: Parameters<typeof addTransaction>[0]
  ) => {
    await addTransaction(newTransaction);
    setIsModalOpen(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading transactions...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-4 md:p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="hidden md:block">
            <h1 className="text-2xl md:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              Expense Manager
            </h1>
            <p className="text-muted-foreground">
              Track your monthly income and expenses
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => navigate("/reports")}
              className="flex items-center gap-2"
            >
              <BarChart3 className="h-4 w-4" />
              Reports
            </Button>
            {isAdmin && (
              <Button
                variant="outline"
                onClick={() => navigate("/users")}
                className="flex items-center gap-2"
              >
                <UserCheck className="h-4 w-4" />
                Users
              </Button>
            )}
            <Button
              variant="outline"
              onClick={() => navigate("/shares")}
              className="flex items-center gap-2 text-orange-500 border-orange-200 hover:bg-orange-50 hover:text-orange-600"
            >
              <Users className="h-4 w-4" />
              Chia sẻ
            </Button>
            {isAdmin && (
              <Button
                onClick={() => setIsModalOpen(true)}
                className="flex items-center gap-2 hover:bg-primary/85"
              >
                <Plus className="h-4 w-4" />
                Add Transaction
              </Button>
            )}
            <UserBadge />
          </div>
        </div>

        {/* Summary Cards */}
        <SummaryCards balance={balance} transactions={transactions} />

        {/* Transaction Table */}
        <TransactionTable
          transactions={transactions}
          onUpdate={isAdmin ? updateTransaction : undefined}
          onDelete={isAdmin ? deleteTransaction : undefined}
        />

        {/* Add Transaction Modal - Only for admins */}
        {isAdmin && (
          <TransactionModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            onAddTransaction={handleAddTransaction}
          />
        )}
      </div>
    </div>
  );
};

export default Index;
