import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Heart, MessageCircle, Share2, Upload, X, ArrowLeft, Sparkles, TrendingUp, Loader2, Trash2 } from "lucide-react";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useUserName } from "@/hooks/useUserName";
import { usePosts } from "@/hooks/usePosts";
import { useAuth } from "@/hooks/useAuth";
import { format } from "date-fns";
import { vi } from "date-fns/locale";

interface ShareProps {
  onBack?: () => void;
}

export const Share = ({ onBack }: ShareProps) => {
  const { posts, loading, error, createPost, addComment, likePost, deletePost, deleteComment } = usePosts();
  const [newPostContent, setNewPostContent] = useState("");
  const [newPostImage, setNewPostImage] = useState<string | null>(null);
  const [commentInputs, setCommentInputs] = useState<Record<string, string>>({});
  const [isCreatingPost, setIsCreatingPost] = useState(false);
  const [commentingOnPost, setCommentingOnPost] = useState<string | null>(null);
  const [deletingPost, setDeletingPost] = useState<string | null>(null);
  const [deletingComment, setDeletingComment] = useState<string | null>(null);
  const { name: userName, ensureName } = useUserName();
  const { isAdmin } = useAuth();

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewPostImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCreatePost = async () => {
    if (!newPostContent.trim()) return;
    
    const currentUserName = ensureName();
    if (!currentUserName) return;

    setIsCreatingPost(true);
    
    const result = await createPost({
      user_name: currentUserName,
      content: newPostContent,
      image: newPostImage || undefined,
      avatar: "/placeholder.svg"
    });

    if (result) {
      setNewPostContent("");
      setNewPostImage(null);
    }
    
    setIsCreatingPost(false);
  };

  const handleAddComment = async (postId: string) => {
    const commentContent = commentInputs[postId];
    if (!commentContent?.trim()) return;

    const currentUserName = ensureName();
    if (!currentUserName) return;

    setCommentingOnPost(postId);

    const result = await addComment({
      post_id: postId,
      user_name: currentUserName,
      content: commentContent
    });

    if (result) {
      setCommentInputs({ ...commentInputs, [postId]: "" });
    }
    
    setCommentingOnPost(null);
  };

  const handleLike = async (postId: string) => {
    const currentUserName = ensureName();
    if (!currentUserName) return;

    await likePost(postId, currentUserName);
  };

  const handleDeletePost = async (postId: string) => {
    setDeletingPost(postId);
    const success = await deletePost(postId);
    if (success) {
      // Success feedback could be added here (toast notification)
    }
    setDeletingPost(null);
  };

  const handleDeleteComment = async (commentId: string, postId: string) => {
    setDeletingComment(commentId);
    const success = await deleteComment(commentId, postId);
    if (success) {
      // Success feedback could be added here (toast notification)
    }
    setDeletingComment(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-orange-500 via-amber-500 to-orange-600 text-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="flex items-center gap-4 mb-4">
            {onBack && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onBack}
                className="text-white hover:bg-white/20 backdrop-blur-sm"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            )}
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                <Sparkles className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold">
                  Chia sẻ
                </h1>
                <p className="text-orange-100 text-lg">
                  Cộng đồng quản lý tài chính thông minh
                </p>
              </div>
            </div>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 mt-6">
            <div className="text-center">
              <div className="text-2xl font-bold">1.2k+</div>
              <div className="text-orange-100 text-sm">Thành viên</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">250+</div>
              <div className="text-orange-100 text-sm">Bài viết</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">89%</div>
              <div className="text-orange-100 text-sm">Tiết kiệm thành công</div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-3xl mx-auto px-4 py-8 space-y-8">
        {/* Create Post Form */}
        <Card className="overflow-hidden border-0 shadow-xl shadow-orange-100/50 bg-white/80 backdrop-blur-sm">
          <div className="bg-gradient-to-r from-orange-500 to-amber-500 h-1"></div>
          <div className="p-6 space-y-6">
            <div className="flex items-center gap-4">
              <Avatar className="h-12 w-12 ring-2 ring-orange-200">
                <AvatarImage src="/placeholder.svg" />
                <AvatarFallback className="bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold">
                  {userName?.charAt(0).toUpperCase() || "?"}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-semibold text-lg">Chia sẻ kinh nghiệm của bạn</p>
                <p className="text-sm text-muted-foreground">Giúp cộng đồng học hỏi từ câu chuyện của bạn</p>
              </div>
            </div>
            
            <Textarea
              placeholder="Hôm nay bạn có kinh nghiệm gì về quản lý tài chính muốn chia sẻ? 💡"
              value={newPostContent}
              onChange={(e) => setNewPostContent(e.target.value)}
              className="min-h-[120px] resize-none border-orange-200 focus:border-orange-400 focus:ring-orange-200 bg-white/50 backdrop-blur-sm text-base placeholder:text-muted-foreground/70"
              disabled={isCreatingPost}
            />
            
            {newPostImage && (
              <div className="relative group">
                <img 
                  src={newPostImage} 
                  alt="Preview" 
                  className="max-h-80 w-full object-cover rounded-xl shadow-lg"
                />
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg"
                  onClick={() => setNewPostImage(null)}
                  disabled={isCreatingPost}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  disabled={isCreatingPost}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('image-upload')?.click()}
                  className="border-orange-200 text-orange-600 hover:bg-orange-50 hover:text-orange-700 hover:border-orange-300"
                  disabled={isCreatingPost}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Thêm ảnh
                </Button>
              </div>
              
              <Button
                onClick={handleCreatePost}
                disabled={!newPostContent.trim() || isCreatingPost}
                className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white px-6 py-2 font-semibold shadow-lg shadow-orange-200 transition-all duration-200 hover:shadow-xl hover:shadow-orange-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isCreatingPost ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Đang đăng...
                  </>
                ) : (
                  <>
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Đăng bài
                  </>
                )}
              </Button>
            </div>
          </div>
        </Card>

        {/* Error State */}
        {error && (
          <Card className="p-4 border-red-200 bg-red-50">
            <p className="text-red-600 text-center">{error}</p>
          </Card>
        )}

        {/* Loading State */}
        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
          </div>
        ) : (
          /* Posts Feed */
          <div className="space-y-6">
            {posts.map((post, index) => (
              <Card key={post.id} className="overflow-hidden border-0 shadow-xl shadow-gray-100/50 bg-white/80 backdrop-blur-sm animate-fade-in hover:shadow-2xl hover:shadow-gray-200/60 transition-all duration-300" style={{ animationDelay: `${index * 100}ms` }}>
                <div className="p-6 space-y-4">
                  {/* Post Header */}
                  <div className="flex items-center gap-4">
                    <Avatar className="h-14 w-14 ring-2 ring-orange-200 hover:ring-orange-300 transition-all duration-200">
                      <AvatarImage src={post.avatar || "/placeholder.svg"} />
                      <AvatarFallback className="bg-gradient-to-br from-orange-400 to-amber-400 text-white font-bold text-lg">
                        {post.user_name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <p className="font-semibold text-lg text-gray-800">{post.user_name}</p>
                        <div className="px-2 py-1 bg-gradient-to-r from-orange-100 to-amber-100 rounded-full">
                          <span className="text-xs font-medium text-orange-700">Thành viên tích cực</span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground flex items-center gap-1">
                        <span>{format(new Date(post.created_at), "dd 'tháng' MM, yyyy 'lúc' HH:mm", { locale: vi })}</span>
                        <span className="text-orange-500">•</span>
                        <span className="text-orange-600">Công khai</span>
                      </p>
                    </div>
                    {/* Admin Delete Button */}
                    {isAdmin && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors duration-200"
                            disabled={deletingPost === post.id}
                          >
                            {deletingPost === post.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Xóa bài viết</AlertDialogTitle>
                            <AlertDialogDescription>
                              Bạn có chắc chắn muốn xóa bài viết này không? Hành động này không thể hoàn tác và sẽ xóa tất cả bình luận và lượt thích liên quan.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Hủy</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeletePost(post.id)}
                              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                            >
                              Xóa bài viết
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                  </div>

                  {/* Post Content */}
                  <div className="pl-2">
                    <p className="text-gray-700 leading-relaxed text-base whitespace-pre-wrap">{post.content}</p>
                  </div>

                  {/* Post Image */}
                  {post.image && (
                    <div className="relative group">
                      <img 
                        src={post.image} 
                        alt="Post image" 
                        className="w-full max-h-96 object-cover rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 rounded-xl transition-colors duration-300"></div>
                    </div>
                  )}

                  {/* Post Actions */}
                  <div className="flex items-center justify-between py-3 border-t border-orange-100">
                    <div className="flex items-center gap-6">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleLike(post.id)}
                        className="text-gray-600 hover:text-orange-500 hover:bg-orange-50 transition-colors duration-200 font-medium"
                      >
                        <Heart className="h-5 w-5 mr-2 hover:scale-110 transition-transform duration-200" />
                        <span>{post.likes_count}</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 hover:text-blue-500 hover:bg-blue-50 transition-colors duration-200 font-medium"
                      >
                        <MessageCircle className="h-5 w-5 mr-2 hover:scale-110 transition-transform duration-200" />
                        <span>{post.comments.length}</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 hover:text-green-500 hover:bg-green-50 transition-colors duration-200 font-medium"
                      >
                        <Share2 className="h-5 w-5 mr-2 hover:scale-110 transition-transform duration-200" />
                        Chia sẻ
                      </Button>
                    </div>
                    
                    {post.likes_count > 10 && (
                      <div className="flex items-center gap-1 text-orange-600 bg-orange-50 px-3 py-1 rounded-full">
                        <TrendingUp className="h-4 w-4" />
                        <span className="text-sm font-medium">Trending</span>
                      </div>
                    )}
                  </div>

                  {/* Comments */}
                  <div className="space-y-4 pt-2">
                    {post.comments.map((comment) => (
                      <div key={comment.id} className="bg-gradient-to-r from-gray-50 to-orange-50/30 rounded-xl p-4 ml-4 border-l-4 border-orange-200 hover:shadow-md transition-shadow duration-200">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-400 text-white text-sm font-medium">
                                {comment.user_name.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium text-sm text-gray-800">{comment.user_name}</p>
                              <p className="text-xs text-muted-foreground">
                                {format(new Date(comment.created_at), "dd/MM 'lúc' HH:mm", { locale: vi })}
                              </p>
                            </div>
                          </div>
                          {/* Admin Delete Comment Button */}
                          {isAdmin && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors duration-200"
                                  disabled={deletingComment === comment.id}
                                >
                                  {deletingComment === comment.id ? (
                                    <Loader2 className="h-3 w-3 animate-spin" />
                                  ) : (
                                    <Trash2 className="h-3 w-3" />
                                  )}
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Xóa bình luận</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Bạn có chắc chắn muốn xóa bình luận này không? Hành động này không thể hoàn tác.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Hủy</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteComment(comment.id, post.id)}
                                    className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                                  >
                                    Xóa bình luận
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </div>
                        <p className="text-sm text-gray-700 ml-11">{comment.content}</p>
                      </div>
                    ))}

                    {/* Add Comment */}
                    <div className="flex gap-3 mt-4 pt-4 border-t border-orange-100">
                      <Avatar className="h-10 w-10 flex-shrink-0">
                        <AvatarFallback className="bg-gradient-to-br from-orange-400 to-amber-400 text-white font-medium">
                          {userName?.charAt(0).toUpperCase() || "?"}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex gap-2 flex-1">
                        <Input
                          placeholder="Viết bình luận..."
                          value={commentInputs[post.id] || ""}
                          onChange={(e) => setCommentInputs({
                            ...commentInputs,
                            [post.id]: e.target.value
                          })}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              handleAddComment(post.id);
                            }
                          }}
                          className="border-orange-200 focus:border-orange-400 focus:ring-orange-200 bg-white/70 backdrop-blur-sm"
                          disabled={commentingOnPost === post.id}
                        />
                        <Button
                          size="sm"
                          onClick={() => handleAddComment(post.id)}
                          disabled={!commentInputs[post.id]?.trim() || commentingOnPost === post.id}
                          className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white px-4 font-medium shadow-md hover:shadow-lg transition-all duration-200"
                        >
                          {commentingOnPost === post.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            "Gửi"
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};