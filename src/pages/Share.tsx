import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Heart, MessageCircle, Share2, Upload, X, ArrowLeft, Sparkles, TrendingUp } from "lucide-react";
import { useUserName } from "@/hooks/useUserName";
import { format } from "date-fns";
import { vi } from "date-fns/locale";

interface Comment {
  id: string;
  user: string;
  content: string;
  timestamp: Date;
}

interface Post {
  id: string;
  user: string;
  avatar?: string;
  content: string;
  image?: string;
  timestamp: Date;
  likes: number;
  comments: Comment[];
}

// Hardcoded demo data
const initialPosts: Post[] = [
  {
    id: "1",
    user: "Nguyễn Văn A",
    avatar: "/placeholder.svg",
    content: "Hôm nay tôi đã tiết kiệm được 100,000 VND bằng cách nấu ăn tại nhà thay vì đi ăn ngoài. Cảm thấy rất hài lòng! 🍳✨ Ai cũng nên thử nhé!",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    likes: 24,
    comments: [
      {
        id: "1",
        user: "Trần Thị B",
        content: "Tuyệt vời! Nấu ăn tại nhà vừa tiết kiệm vừa healthy. Mình cũng đang áp dụng 👍",
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000)
      },
      {
        id: "1a",
        user: "Lê Minh C",
        content: "Wow, 100k một ngày là khá nhiều đấy! Mình cũng muốn học nấu ăn quá",
        timestamp: new Date(Date.now() - 45 * 60 * 1000)
      }
    ]
  },
  {
    id: "2", 
    user: "Lê Thị C",
    avatar: "/placeholder.svg",
    content: "Ai có mẹo nào để quản lý chi tiêu hàng tháng hiệu quả không? Tôi đang gặp khó khăn trong việc cân bằng thu chi. Mọi người chia sẻ kinh nghiệm giúp mình với! 🙏",
    image: "/placeholder.svg",
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
    likes: 18,
    comments: [
      {
        id: "2",
        user: "Phạm Văn D",
        content: "Tôi dùng app này để theo dõi, rất hiệu quả! Nhớ phân loại chi tiêu từng mục nha",
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
      },
      {
        id: "3",
        user: "Hoàng Thị E",
        content: "Nên lập kế hoạch chi tiêu từ đầu tháng và tuân thủ nghiêm ngặt. Rule 50/30/20 rất hay đó!",
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000)
      }
    ]
  },
  {
    id: "3",
    user: "Trần Minh F",
    avatar: "/placeholder.svg", 
    content: "Vừa đạt được mục tiêu tiết kiệm 5 triệu trong tháng này! 🎉 Bí quyết là cắt giảm những chi tiêu không cần thiết và đầu tư vào những thứ thực sự quan trọng.",
    timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
    likes: 35,
    comments: [
      {
        id: "4",
        user: "Nguyễn Thị G",
        content: "Chúc mừng bạn! Chia sẻ bí quyết cụ thể được không? 🤔",
        timestamp: new Date(Date.now() - 7 * 60 * 60 * 1000)
      }
    ]
  }
];

interface ShareProps {
  onBack?: () => void;
}

export const Share = ({ onBack }: ShareProps) => {
  const [posts, setPosts] = useState<Post[]>(initialPosts);
  const [newPostContent, setNewPostContent] = useState("");
  const [newPostImage, setNewPostImage] = useState<string | null>(null);
  const [commentInputs, setCommentInputs] = useState<Record<string, string>>({});
  const { name: userName, ensureName } = useUserName();

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewPostImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCreatePost = () => {
    if (!newPostContent.trim()) return;
    
    const currentUserName = ensureName();
    if (!currentUserName) return;

    const newPost: Post = {
      id: Date.now().toString(),
      user: currentUserName,
      content: newPostContent,
      image: newPostImage || undefined,
      timestamp: new Date(),
      likes: 0,
      comments: []
    };

    setPosts([newPost, ...posts]);
    setNewPostContent("");
    setNewPostImage(null);
  };

  const handleAddComment = (postId: string) => {
    const commentContent = commentInputs[postId];
    if (!commentContent?.trim()) return;

    const currentUserName = ensureName();
    if (!currentUserName) return;

    const newComment: Comment = {
      id: Date.now().toString(),
      user: currentUserName,
      content: commentContent,
      timestamp: new Date()
    };

    setPosts(posts.map(post => 
      post.id === postId 
        ? { ...post, comments: [...post.comments, newComment] }
        : post
    ));

    setCommentInputs({ ...commentInputs, [postId]: "" });
  };

  const handleLike = (postId: string) => {
    setPosts(posts.map(post =>
      post.id === postId 
        ? { ...post, likes: post.likes + 1 }
        : post
    ));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-orange-500 via-amber-500 to-orange-600 text-white">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="flex items-center gap-4 mb-4">
            {onBack && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onBack}
                className="text-white hover:bg-white/20 backdrop-blur-sm"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            )}
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                <Sparkles className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-bold">
                  Chia sẻ
                </h1>
                <p className="text-orange-100 text-lg">
                  Cộng đồng quản lý tài chính thông minh
                </p>
              </div>
            </div>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 mt-6">
            <div className="text-center">
              <div className="text-2xl font-bold">1.2k+</div>
              <div className="text-orange-100 text-sm">Thành viên</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">250+</div>
              <div className="text-orange-100 text-sm">Bài viết</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">89%</div>
              <div className="text-orange-100 text-sm">Tiết kiệm thành công</div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-3xl mx-auto px-4 py-8 space-y-8">
        {/* Create Post Form */}
        <Card className="overflow-hidden border-0 shadow-xl shadow-orange-100/50 bg-white/80 backdrop-blur-sm">
          <div className="bg-gradient-to-r from-orange-500 to-amber-500 h-1"></div>
          <div className="p-6 space-y-6">
            <div className="flex items-center gap-4">
              <Avatar className="h-12 w-12 ring-2 ring-orange-200">
                <AvatarImage src="/placeholder.svg" />
                <AvatarFallback className="bg-gradient-to-r from-orange-500 to-amber-500 text-white font-semibold">
                  {userName?.charAt(0).toUpperCase() || "?"}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-semibold text-lg">Chia sẻ kinh nghiệm của bạn</p>
                <p className="text-sm text-muted-foreground">Giúp cộng đồng học hỏi từ câu chuyện của bạn</p>
              </div>
            </div>
            
            <Textarea
              placeholder="Hôm nay bạn có kinh nghiệm gì về quản lý tài chính muốn chia sẻ? 💡"
              value={newPostContent}
              onChange={(e) => setNewPostContent(e.target.value)}
              className="min-h-[120px] resize-none border-orange-200 focus:border-orange-400 focus:ring-orange-200 bg-white/50 backdrop-blur-sm text-base placeholder:text-muted-foreground/70"
            />
            
            {newPostImage && (
              <div className="relative group">
                <img 
                  src={newPostImage} 
                  alt="Preview" 
                  className="max-h-80 w-full object-cover rounded-xl shadow-lg"
                />
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg"
                  onClick={() => setNewPostImage(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('image-upload')?.click()}
                  className="border-orange-200 text-orange-600 hover:bg-orange-50 hover:text-orange-700 hover:border-orange-300"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Thêm ảnh
                </Button>
              </div>
              
              <Button
                onClick={handleCreatePost}
                disabled={!newPostContent.trim()}
                className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white px-6 py-2 font-semibold shadow-lg shadow-orange-200 transition-all duration-200 hover:shadow-xl hover:shadow-orange-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Đăng bài
              </Button>
            </div>
          </div>
        </Card>

        {/* Posts Feed */}
        <div className="space-y-6">
          {posts.map((post, index) => (
            <Card key={post.id} className="overflow-hidden border-0 shadow-xl shadow-gray-100/50 bg-white/80 backdrop-blur-sm animate-fade-in hover:shadow-2xl hover:shadow-gray-200/60 transition-all duration-300" style={{ animationDelay: `${index * 100}ms` }}>
              <div className="p-6 space-y-4">
                {/* Post Header */}
                <div className="flex items-center gap-4">
                  <Avatar className="h-14 w-14 ring-2 ring-orange-200 hover:ring-orange-300 transition-all duration-200">
                    <AvatarImage src={post.avatar} />
                    <AvatarFallback className="bg-gradient-to-br from-orange-400 to-amber-400 text-white font-bold text-lg">
                      {post.user.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <p className="font-semibold text-lg text-gray-800">{post.user}</p>
                      <div className="px-2 py-1 bg-gradient-to-r from-orange-100 to-amber-100 rounded-full">
                        <span className="text-xs font-medium text-orange-700">Thành viên tích cực</span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground flex items-center gap-1">
                      <span>{format(post.timestamp, "dd 'tháng' MM, yyyy 'lúc' HH:mm", { locale: vi })}</span>
                      <span className="text-orange-500">•</span>
                      <span className="text-orange-600">Công khai</span>
                    </p>
                  </div>
                </div>

                {/* Post Content */}
                <div className="pl-2">
                  <p className="text-gray-700 leading-relaxed text-base whitespace-pre-wrap">{post.content}</p>
                </div>

                {/* Post Image */}
                {post.image && (
                  <div className="relative group">
                    <img 
                      src={post.image} 
                      alt="Post image" 
                      className="w-full max-h-96 object-cover rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 rounded-xl transition-colors duration-300"></div>
                  </div>
                )}

                {/* Post Actions */}
                <div className="flex items-center justify-between py-3 border-t border-orange-100">
                  <div className="flex items-center gap-6">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleLike(post.id)}
                      className="text-gray-600 hover:text-orange-500 hover:bg-orange-50 transition-colors duration-200 font-medium"
                    >
                      <Heart className="h-5 w-5 mr-2 hover:scale-110 transition-transform duration-200" />
                      <span>{post.likes}</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-600 hover:text-blue-500 hover:bg-blue-50 transition-colors duration-200 font-medium"
                    >
                      <MessageCircle className="h-5 w-5 mr-2 hover:scale-110 transition-transform duration-200" />
                      <span>{post.comments.length}</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-600 hover:text-green-500 hover:bg-green-50 transition-colors duration-200 font-medium"
                    >
                      <Share2 className="h-5 w-5 mr-2 hover:scale-110 transition-transform duration-200" />
                      Chia sẻ
                    </Button>
                  </div>
                  
                  {post.likes > 10 && (
                    <div className="flex items-center gap-1 text-orange-600 bg-orange-50 px-3 py-1 rounded-full">
                      <TrendingUp className="h-4 w-4" />
                      <span className="text-sm font-medium">Trending</span>
                    </div>
                  )}
                </div>

                {/* Comments */}
                <div className="space-y-4 pt-2">
                  {post.comments.map((comment) => (
                    <div key={comment.id} className="bg-gradient-to-r from-gray-50 to-orange-50/30 rounded-xl p-4 ml-4 border-l-4 border-orange-200 hover:shadow-md transition-shadow duration-200">
                      <div className="flex items-center gap-3 mb-2">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-400 text-white text-sm font-medium">
                            {comment.user.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-sm text-gray-800">{comment.user}</p>
                          <p className="text-xs text-muted-foreground">
                            {format(comment.timestamp, "dd/MM 'lúc' HH:mm", { locale: vi })}
                          </p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700 ml-11">{comment.content}</p>
                    </div>
                  ))}

                  {/* Add Comment */}
                  <div className="flex gap-3 mt-4 pt-4 border-t border-orange-100">
                    <Avatar className="h-10 w-10 flex-shrink-0">
                      <AvatarFallback className="bg-gradient-to-br from-orange-400 to-amber-400 text-white font-medium">
                        {userName?.charAt(0).toUpperCase() || "?"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex gap-2 flex-1">
                      <Input
                        placeholder="Viết bình luận..."
                        value={commentInputs[post.id] || ""}
                        onChange={(e) => setCommentInputs({
                          ...commentInputs,
                          [post.id]: e.target.value
                        })}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            handleAddComment(post.id);
                          }
                        }}
                        className="border-orange-200 focus:border-orange-400 focus:ring-orange-200 bg-white/70 backdrop-blur-sm"
                      />
                      <Button
                        size="sm"
                        onClick={() => handleAddComment(post.id)}
                        disabled={!commentInputs[post.id]?.trim()}
                        className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white px-4 font-medium shadow-md hover:shadow-lg transition-all duration-200"
                      >
                        Gửi
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};