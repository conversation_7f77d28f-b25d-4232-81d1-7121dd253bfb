import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, TrendingUp, TrendingDown, DollarSign } from "lucide-react";
import { Transaction } from "@/hooks/useTransactions";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from "recharts";
import { format, startOfMonth, endOfMonth, eachMonthOfInterval, subMonths } from "date-fns";

interface ReportsProps {
  transactions: Transaction[];
  onBack: () => void;
}

const COLORS = ['hsl(var(--destructive))', 'hsl(var(--primary))', 'hsl(var(--success))', 'hsl(var(--muted))'];

export function Reports({ transactions, onBack }: ReportsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  // Get current month data
  const now = new Date();
  const currentMonthStart = startOfMonth(now);
  const currentMonthEnd = endOfMonth(now);
  
  const currentMonthTransactions = transactions.filter(t => {
    const transactionDate = new Date(t.date);
    return transactionDate >= currentMonthStart && transactionDate <= currentMonthEnd;
  });

  const currentMonthIn = currentMonthTransactions
    .filter(t => t.type === "money-in")
    .reduce((sum, t) => sum + t.amount, 0);

  const currentMonthOut = currentMonthTransactions
    .filter(t => t.type === "money-out")
    .reduce((sum, t) => sum + t.amount, 0);

  // Top 3 biggest expenses
  const expenses = transactions
    .filter(t => t.type === "money-out")
    .sort((a, b) => b.amount - a.amount)
    .slice(0, 3);

  // Monthly data for the last 6 months
  const last6Months = eachMonthOfInterval({
    start: subMonths(now, 5),
    end: now
  });

  const monthlyData = last6Months.map(month => {
    const monthStart = startOfMonth(month);
    const monthEnd = endOfMonth(month);
    
    const monthTransactions = transactions.filter(t => {
      const transactionDate = new Date(t.date);
      return transactionDate >= monthStart && transactionDate <= monthEnd;
    });

    const moneyIn = monthTransactions
      .filter(t => t.type === "money-in")
      .reduce((sum, t) => sum + t.amount, 0);

    const moneyOut = monthTransactions
      .filter(t => t.type === "money-out")
      .reduce((sum, t) => sum + t.amount, 0);

    return {
      month: format(month, "MMM"),
      moneyIn,
      moneyOut,
      net: moneyIn - moneyOut
    };
  });

  // Balance history
  const balanceHistory = transactions
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .map((transaction, index) => ({
      date: format(new Date(transaction.date), "dd/MM"),
      balance: transaction.balance
    }))
    .slice(-10); // Last 10 transactions

  // Pie chart data
  const pieData = [
    { name: "Money In", value: currentMonthIn, color: COLORS[2] },
    { name: "Money Out", value: currentMonthOut, color: COLORS[0] }
  ];

  return (
    <div className="min-h-screen bg-background p-4 md:p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-foreground">Reports & Analysis</h1>
            <p className="text-muted-foreground">Insights into your spending patterns</p>
          </div>
        </div>

        {/* Current Month Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="shadow-soft">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-success" />
                <p className="text-sm font-medium text-muted-foreground">Money In</p>
              </div>
              <p className="text-2xl font-bold text-success">{formatCurrency(currentMonthIn)}</p>
            </CardContent>
          </Card>
          
          <Card className="shadow-soft">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <TrendingDown className="h-4 w-4 text-destructive" />
                <p className="text-sm font-medium text-muted-foreground">Money Out</p>
              </div>
              <p className="text-2xl font-bold text-destructive">{formatCurrency(currentMonthOut)}</p>
            </CardContent>
          </Card>
          
          <Card className="shadow-soft">
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-primary" />
                <p className="text-sm font-medium text-muted-foreground">Net</p>
              </div>
              <p className={`text-2xl font-bold ${currentMonthIn - currentMonthOut >= 0 ? 'text-success' : 'text-destructive'}`}>
                {formatCurrency(currentMonthIn - currentMonthOut)}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Bar Chart */}
          <Card className="shadow-soft">
            <CardHeader>
              <CardTitle>Monthly In/Out (Last 6 Months)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={monthlyData}>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => (value / 1000000).toFixed(0) + "M"} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Bar dataKey="moneyIn" fill="hsl(var(--success))" name="Money In" />
                    <Bar dataKey="moneyOut" fill="hsl(var(--destructive))" name="Money Out" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Pie Chart */}
          <Card className="shadow-soft">
            <CardHeader>
              <CardTitle>Current Month Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Bottom Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Expenses */}
          <Card className="shadow-soft">
            <CardHeader>
              <CardTitle>Top 3 Biggest Expenses</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {expenses.length > 0 ? expenses.map((expense, index) => (
                  <div key={expense.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                        index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : 'bg-amber-600'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{expense.note || "No note"}</p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(expense.date), "dd/MM/yyyy")}
                        </p>
                      </div>
                    </div>
                    <p className="font-semibold text-destructive">
                      {formatCurrency(expense.amount)}
                    </p>
                  </div>
                )) : (
                  <p className="text-muted-foreground text-center py-4">No expenses recorded yet</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Balance History */}
          <Card className="shadow-soft">
            <CardHeader>
              <CardTitle>Balance History (Last 10 Transactions)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={balanceHistory}>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis dataKey="date" />
                    <YAxis tickFormatter={(value) => (value / 1000000).toFixed(0) + "M"} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Line 
                      type="monotone" 
                      dataKey="balance" 
                      stroke="hsl(var(--primary))" 
                      strokeWidth={2}
                      dot={{ fill: "hsl(var(--primary))" }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}