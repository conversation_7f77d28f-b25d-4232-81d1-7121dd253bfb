import { useNavigate } from "react-router-dom";
import { Reports } from "./Reports";
import { useTransactions } from "@/hooks/useTransactions";
import { useAccessLogging } from "@/hooks/useAccessLogging";
import { Loader2 } from "lucide-react";

const ReportsPage = () => {
  const navigate = useNavigate();
  const { transactions, loading } = useTransactions();

  // Initialize access logging
  useAccessLogging();

  const handleBack = () => {
    navigate("/");
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading transactions...</span>
        </div>
      </div>
    );
  }

  return (
    <Reports 
      transactions={transactions} 
      onBack={handleBack} 
    />
  );
};

export default ReportsPage;
