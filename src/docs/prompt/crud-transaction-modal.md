I want to refactor the `src/components/TransactionDetailModal.tsx` component to add editing functionality with the following specific requirements:
(notice that structure of project in type.ts src/integrations/supabase/types.ts)


1. **Add Edit Functionality**:
- Allow users to edit transaction details (amount, date, category, note)
- Add an "Edit Transaction" button next to the existing "Delete Transaction" button
- Both edit and delete buttons should only be visible to users with admin role (using the existing `isAdmin` prop)

2. **Edit Mode Behavior**:
- When the "Edit" button is clicked, transform the modal from view mode to edit mode
- In edit mode, replace the read-only display fields with editable form inputs:
- Amount: editable number input
- Date: date/time picker
- Category: editable input or dropdown
- Note: editable textarea
- Add "Save" and "Cancel" buttons in edit mode
- "Cancel" should revert back to view mode without saving changes
- "Save" should validate and submit the changes

3. **Data Management**:
- After successful update or delete operations, recalculate the account balance
- Ensure the updated balance is displayed correctly in the UI
- Handle proper error states and loading states during save/delete operations

4. **UI/UX Requirements**:
- Maintain the existing modal design and styling
- Ensure smooth transitions between view and edit modes
- Provide appropriate feedback for successful operations and errors
- Form validation should prevent invalid data entry

Please implement this while preserving the existing functionality and maintaining consistency with the current codebase patterns.