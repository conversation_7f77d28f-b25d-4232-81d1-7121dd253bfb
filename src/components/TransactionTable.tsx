import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { TrendingUp, TrendingDown } from "lucide-react";
import { format } from "date-fns";
import { Transaction } from "@/hooks/useTransactions";
import { TransactionDetailModal } from "./TransactionDetailModal";
import { useState } from "react";

interface TransactionTableProps {
  transactions: Transaction[];
  onUpdate?: (
    id: string,
    updates: Partial<Pick<Transaction, "amount" | "date" | "category" | "note">>
  ) => Promise<boolean>;
  onDelete?: (id: string) => Promise<void>;
}

export function TransactionTable({ transactions, onUpdate, onDelete }: TransactionTableProps) {
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  const sortedTransactions = [...transactions].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  const handleTransactionClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setIsDetailOpen(true);
  };

  if (transactions.length === 0) {
    return (
      <Card className="shadow-soft">
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <p>No transactions yet.</p>
            <p className="text-sm">Add your first transaction to get started!</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="shadow-soft">
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="space-y-1">
            {sortedTransactions.map((transaction) => (
              <div
                key={transaction.id}
                onClick={() => handleTransactionClick(transaction)}
                className="flex items-center gap-3 p-4 hover:bg-muted/50 cursor-pointer transition-colors border-b last:border-b-0"
              >
                {/* Column 1: Icon */}
                <div className="flex-shrink-0">
                  {transaction.type === "money-in" ? (
                    <div className="w-10 h-10 rounded-full bg-success/10 flex items-center justify-center">
                      <TrendingUp className="w-5 h-5 text-success" />
                    </div>
                  ) : (
                    <div className="w-10 h-10 rounded-full bg-destructive/10 flex items-center justify-center">
                      <TrendingDown className="w-5 h-5 text-destructive" />
                    </div>
                  )}
                </div>

                {/* Column 2: Note and Date */}
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-sm truncate">
                    {transaction.note || "No note"}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {format(new Date(transaction.date), "dd/MM/yy 'at' HH:mm")}
                  </p>
                </div>

                {/* Column 3: Amount */}
                <div className="flex-shrink-0 text-right">
                  <p className={`font-semibold text-sm ${
                    transaction.type === "money-in" ? "text-success" : "text-destructive"
                  }`}>
                    {transaction.type === "money-in" ? "+" : "-"}{formatCurrency(transaction.amount)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <TransactionDetailModal
        transaction={selectedTransaction}
        isOpen={isDetailOpen}
        onClose={() => setIsDetailOpen(false)}
        onUpdate={onUpdate}
        onDelete={onDelete}
      />
    </>
  );
}