import { Card, CardContent } from "@/components/ui/card";
import { TrendingUp, TrendingDown, Wallet } from "lucide-react";
import { Transaction } from "@/hooks/useTransactions";

interface SummaryCardsProps {
  balance: number;
  transactions: Transaction[];
}

export function SummaryCards({ balance, transactions }: SummaryCardsProps) {
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  
  const thisMonthTransactions = transactions.filter(t => {
    const transactionDate = new Date(t.date);
    return transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear;
  });
  
  const moneyIn = thisMonthTransactions
    .filter(t => t.type === "money-in")
    .reduce((sum, t) => sum + t.amount, 0);
    
  const moneyOut = thisMonthTransactions
    .filter(t => t.type === "money-out")
    .reduce((sum, t) => sum + t.amount, 0);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      <Card className="col-span-2 md:col-span-1 shadow-soft border-0 bg-primary/10">
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
              <Wallet className="h-4 w-4 text-primary" />
            </div>
            <p className="text-sm font-medium text-muted-foreground">Current Balance</p>
          </div>
          <p className="text-2xl font-bold mt-2">{formatCurrency(balance)}</p>
        </CardContent>
      </Card>
      
      <Card className="shadow-soft border-0 bg-transparent">
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-lg bg-success/10 flex items-center justify-center">
              <TrendingUp className="h-4 w-4 text-success" />
            </div>
            <p className="text-sm font-medium text-muted-foreground">Money In (This Month)</p>
          </div>
          <p className="text-2xl font-bold text-success mt-2">{formatCurrency(moneyIn)}</p>
        </CardContent>
      </Card>
      
      <Card className="shadow-soft border-0 bg-transparent">
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-lg bg-destructive/10 flex items-center justify-center">
              <TrendingDown className="h-4 w-4 text-destructive" />
            </div>
            <p className="text-sm font-medium text-muted-foreground">Money Out (This Month)</p>
          </div>
          <p className="text-2xl font-bold text-destructive mt-2">{formatCurrency(moneyOut)}</p>
        </CardContent>
      </Card>
    </div>
  );
}