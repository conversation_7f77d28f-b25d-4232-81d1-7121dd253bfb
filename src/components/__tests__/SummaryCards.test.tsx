import { describe, it, expect } from 'vitest';
import { renderToString } from 'react-dom/server';
import { SummaryCards } from '../SummaryCards';

const sampleTxns: any[] = [
  { id: '1', type: 'money-in', amount: 500000, date: new Date().toISOString(), note: 'salary' },
  { id: '2', type: 'money-out', amount: 200000, date: new Date().toISOString(), note: 'groceries' },
];

describe('SummaryCards responsive layout and styling', () => {
  it('renders layout with mobile row for last two cards and emphasized first card', () => {
    const html = renderToString(
      <SummaryCards balance={1000000} transactions={sampleTxns as any} />
    );

    // Container grid: mobile two columns, desktop three columns (row of three)
    expect(html).toContain('grid grid-cols-2 md:grid-cols-3');

    // First card spans full width on mobile, single column on md+
    expect(html).toContain('col-span-2 md:col-span-1');

    // First card emphasized background
    expect(html).toContain('bg-primary/10');

    // Other cards are subtle (transparent background)
    expect(html).toContain('bg-transparent');

    // Contains the labels for verification
    expect(html).toContain('Current Balance');
    expect(html).toContain('Money In (This Month)');
    expect(html).toContain('Money Out (This Month)');
  });
});
