import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Home, BarChart3, Users, UserCheck } from "lucide-react";
import { cn } from "@/lib/utils";

interface LayoutProps {
  children: React.ReactNode;
  showNavigation?: boolean;
}

export const Layout = ({ children, showNavigation = true }: LayoutProps) => {
  const navigate = useNavigate();
  const location = useLocation();

  if (!showNavigation) {
    return <>{children}</>;
  }

  const navigationItems = [
    {
      label: "Dashboard",
      icon: Home,
      path: "/",
      active: location.pathname === "/",
    },
    {
      label: "Reports",
      icon: BarChart3,
      path: "/reports",
      active: location.pathname === "/reports",
    },
    {
      label: "Users",
      icon: UserCheck,
      path: "/users",
      active: location.pathname === "/users",
    },
    {
      label: "Chia sẻ",
      icon: Users,
      path: "/shares",
      active: location.pathname === "/shares",
      className: "text-orange-500 border-orange-200 hover:bg-orange-50 hover:text-orange-600",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Fixed Navigation Header */}
      <nav className="sticky top-0 z-10 bg-background/80 backdrop-blur-md border-b border-border">
        <div className="max-w-6xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Logo/Title */}
            <div className="flex items-center gap-4">
              <h1 
                className="text-xl md:text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent cursor-pointer"
                onClick={() => navigate("/")}
              >
                Expense Manager
              </h1>
            </div>

            {/* Navigation Links */}
            <div className="flex gap-2">
              {navigationItems.map((item) => (
                <Button
                  key={item.path}
                  variant={item.active ? "default" : "outline"}
                  size="sm"
                  onClick={() => navigate(item.path)}
                  className={cn(
                    "flex items-center gap-2 transition-all duration-200",
                    item.active 
                      ? "bg-primary text-primary-foreground shadow-md" 
                      : "hover:scale-105",
                    item.className
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{item.label}</span>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main>
        {children}
      </main>
    </div>
  );
};
