import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { User, LogOut, Shield, Eye, Pencil } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useUserName } from "@/hooks/useUserName";

export const UserBadge = () => {
  const { user, profile, signOut, isAdmin } = useAuth();
  const { name, ensureName, promptForName } = useUserName();
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  // For guests, prompt for name on first mount if missing
  useEffect(() => {
    if (!user && !name) {
      ensureName();
    }
  }, [user, name, ensureName]);

  const getInitialsFromName = (n?: string | null) => {
    const base = (n ?? "Guest").trim();
    if (!base) return "G";
    return base
      .split(/\s+|\./)
      .filter(Boolean)
      .slice(0, 2)
      .map((part) => part.charAt(0).toUpperCase())
      .join("");
  };

  const handleSignOut = () => {
    setIsOpen(false);
    signOut();
  };

  const displayName = name || (user?.email ? user.email.split("@")[0] : "Guest");

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2 h-auto px-3 py-2">
          <Avatar className="h-6 w-6">
            <AvatarFallback className="text-xs bg-primary text-primary-foreground">
              {getInitialsFromName(displayName)}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col items-start">
            <div className="flex items-center gap-2">
              <span className="hidden md:inline text-sm font-medium truncate max-w-24">
                {displayName}
              </span>
              <Badge 
                variant={isAdmin ? "default" : "secondary"} 
                className="hidden md:inline-flex items-center gap-1 text-xs py-0 px-1 h-4 leading-none whitespace-nowrap"
              >
                {isAdmin ? (
                  <>
                    <Shield className="h-2 w-2" />
                    <span>Admin</span>
                  </>
                ) : (
                  <>
                    <Eye className="h-2 w-2" />
                    <span>{user ? "User" : "Guest"}</span>
                  </>
                )}
              </Badge>
            </div>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Profile summary */}
        <DropdownMenuItem disabled className="flex flex-col items-start p-3">
          <div className="flex items-center gap-2 w-full">
            <User className="h-4 w-4" />
            <span className="font-medium">Profile</span>
          </div>
          <div className="text-xs text-muted-foreground mt-1 w-full">
            <div className="flex items-center justify-between">
              <span>Name:</span>
              <span className="font-medium">{displayName}</span>
            </div>
            {user && (
              <div className="mt-1 break-all">{user.email}</div>
            )}
            {user && (
              <div className="flex items-center gap-1 mt-1">
                <span>Role:</span>
                <Badge 
                  variant={isAdmin ? "default" : "secondary"} 
                  className="text-xs"
                >
                  {profile?.role ?? "user"}
                </Badge>
              </div>
            )}
          </div>
        </DropdownMenuItem>

        {/* Guest: allow setting/changing name */}
        {!user && (
          <>
            <DropdownMenuItem onClick={() => { setIsOpen(false); promptForName(); }}>
              <Pencil className="mr-2 h-4 w-4" />
              <span>{name ? "Change name" : "Set your name"}</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => { setIsOpen(false); navigate('/auth'); }}>
              <User className="mr-2 h-4 w-4" />
              <span>Login</span>
            </DropdownMenuItem>
          </>
        )}

        {/* Authenticated: sign out */}
        {user && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleSignOut} className="text-destructive focus:text-destructive">
              <LogOut className="mr-2 h-4 w-4" />
              <span>Sign out</span>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};