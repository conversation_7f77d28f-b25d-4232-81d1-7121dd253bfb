import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { Layout } from '@/components/Layout';
import { Loader2, AlertCircle, Shield } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface AdminRouteGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
  showLayout?: boolean;
}

export const AdminRouteGuard = ({ 
  children, 
  redirectTo = '/', 
  showLayout = true 
}: AdminRouteGuardProps) => {
  const { isAdmin, isAuthenticated, loading } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        // Guest users - redirect with error
        toast({
          title: "Access Denied",
          description: "You need to be logged in as an admin to access this page.",
          variant: "destructive",
        });
        navigate(redirectTo, { replace: true });
        return;
      }
      
      if (!isAdmin) {
        // Authenticated but non-admin users - redirect with error
        toast({
          title: "Access Denied", 
          description: "You need admin privileges to access this page.",
          variant: "destructive",
        });
        navigate(redirectTo, { replace: true });
        return;
      }
    }
  }, [isAdmin, isAuthenticated, loading, navigate, toast, redirectTo]);

  const content = () => {
    // Show loading while checking authentication
    if (loading) {
      return (
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Checking permissions...</span>
            </div>
          </div>
        </div>
      );
    }

    // Don't render the page if user is not admin (will be redirected by useEffect)
    if (!isAuthenticated || !isAdmin) {
      return (
        <div className="container mx-auto px-4 py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Access Denied</AlertTitle>
            <AlertDescription>
              You need admin privileges to access this page. You will be redirected shortly.
            </AlertDescription>
          </Alert>
        </div>
      );
    }

    // Render the protected content
    return children;
  };

  if (showLayout) {
    return <Layout>{content()}</Layout>;
  }

  return <>{content()}</>;
};

// Higher-order component version for easier use
export const withAdminGuard = <P extends object>(
  Component: React.ComponentType<P>,
  options?: { redirectTo?: string; showLayout?: boolean }
) => {
  return (props: P) => (
    <AdminRouteGuard 
      redirectTo={options?.redirectTo} 
      showLayout={options?.showLayout}
    >
      <Component {...props} />
    </AdminRouteGuard>
  );
};
