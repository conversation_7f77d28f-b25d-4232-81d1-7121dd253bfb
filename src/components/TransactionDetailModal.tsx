import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { TrendingUp, TrendingDown, Calendar, FileText, Banknote, Tag, Trash2, Pencil, Save, X } from "lucide-react";
import { format } from "date-fns";
import { Transaction } from "@/hooks/useTransactions";
import { useAuth } from "@/hooks/useAuth";
import { useEffect, useState } from "react";

interface TransactionDetailModalProps {
  transaction: Transaction | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdate?: (
    id: string,
    updates: Partial<Pick<Transaction, "amount" | "date" | "category" | "note">>
  ) => Promise<boolean>;
  onDelete?: (id: string) => Promise<void>;
}

export function TransactionDetailModal({ transaction, isOpen, onClose, onUpdate, onDelete }: TransactionDetailModalProps) {
  const { isAdmin } = useAuth();

  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const [amount, setAmount] = useState<number>(0);
  const [date, setDate] = useState<string>(""); // ISO string for input[type=datetime-local]
  const [category, setCategory] = useState<string>("");
  const [note, setNote] = useState<string>("");

  const [errors, setErrors] = useState<{ amount?: string; date?: string }>({});

  // initialize form when transaction changes or when opening
  useEffect(() => {
    if (!transaction) return;
    setAmount(transaction.amount);
    // Convert Date -> yyyy-MM-ddTHH:mm for datetime-local
    const dt = new Date(transaction.date);
    const tzOffset = dt.getTimezoneOffset();
    const local = new Date(dt.getTime() - tzOffset * 60000);
    setDate(local.toISOString().slice(0, 16));
    setCategory(transaction.category || "");
    setNote(transaction.note || "");
    setIsEditing(false);
    setErrors({});
  }, [transaction, isOpen]);

  if (!transaction) return null;

  const handleDelete = async () => {
    if (!transaction || isDeleting) return;
    setIsDeleting(true);
    if (onDelete) {
      await onDelete(transaction.id);
    }
    setIsDeleting(false);
    onClose();
  };

  const validate = () => {
    const next: { amount?: string; date?: string } = {};
    if (!(amount > 0)) next.amount = "Amount must be greater than 0";
    if (!date) next.date = "Date is required";
    setErrors(next);
    return Object.keys(next).length === 0;
  };

  const handleSave = async () => {
    if (!transaction || isSaving) return;
    if (!validate()) return;
    setIsSaving(true);
    let success = false;
    if (onUpdate) {
      success = await onUpdate(transaction.id, {
        amount,
        date: new Date(date),
        category: category || null,
        note: note || null,
      });
    }
    setIsSaving(false);
    if (success) {
      setIsEditing(false);
      onClose(); // close to reflect refreshed data in list/cards
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Banknote className="h-5 w-5" />
            Transaction Details
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Transaction Type */}
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Type</span>
            <Badge 
              variant={transaction.type === "money-in" ? "default" : "destructive"}
              className={`${
                transaction.type === "money-in" 
                  ? "bg-success text-success-foreground hover:bg-success/80" 
                  : ""
              }`}
            >
              {transaction.type === "money-in" ? (
                <TrendingUp className="w-3 h-3 mr-1" />
              ) : (
                <TrendingDown className="w-3 h-3 mr-1" />
              )}
              {transaction.type === "money-in" ? "Money In" : "Money Out"}
            </Badge>
          </div>

          {/* Amount */}
          {!isEditing ? (
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Amount</span>
              <span className={`text-lg font-semibold ${
                transaction.type === "money-in" ? "text-success" : "text-destructive"
              }`}>
                {transaction.type === "money-in" ? "+" : "-"}{formatCurrency(transaction.amount)}
              </span>
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                type="number"
                min={0}
                value={Number.isNaN(amount) ? "" : amount}
                onChange={(e) => setAmount(Number(e.target.value))}
              />
              {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
            </div>
          )}

          {/* Date */}
          {!isEditing ? (
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                Date
              </span>
              <span className="font-medium">
                {format(new Date(transaction.date), "dd/MM/yyyy 'at' HH:mm")}
              </span>
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="datetime-local"
                value={date}
                onChange={(e) => setDate(e.target.value)}
              />
              {errors.date && <p className="text-sm text-destructive">{errors.date}</p>}
            </div>
          )}

          {/* Category */}
          {!isEditing ? (
            transaction.category && (
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground flex items-center gap-1">
                  <Tag className="w-3 h-3" />
                  Category
                </span>
                <Badge variant="outline">{transaction.category}</Badge>
              </div>
            )
          ) : (
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Input
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                placeholder="e.g. Food, Salary"
              />
            </div>
          )}

          {/* Note */}
          {!isEditing ? (
            <div className="space-y-2">
              <span className="text-muted-foreground flex items-center gap-1">
                <FileText className="w-3 h-3" />
                Note
              </span>
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm leading-relaxed">
                  {transaction.note || "No note provided"}
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="note">Note</Label>
              <Textarea
                id="note"
                value={note}
                onChange={(e) => setNote(e.target.value)}
                rows={4}
                className="resize-none text-base"
              />
            </div>
          )}

          {/* Balance After Transaction */}
          <div className="flex items-center justify-between pt-2 border-t">
            <span className="text-muted-foreground">Balance After</span>
            <span className="text-lg font-semibold">
              {formatCurrency(transaction.balance)}
            </span>
          </div>
        </div>

        {isAdmin && (
          <DialogFooter className="border-t pt-4 flex items-center justify-between">
            {!isEditing ? (
              <div className="flex w-full items-center justify-between gap-2">
                <Button
                  variant="secondary"
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-2"
                >
                  <Pencil className="h-4 w-4" />
                  Edit Transaction
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  {isDeleting ? "Deleting..." : "Delete Transaction"}
                </Button>
              </div>
            ) : (
              <div className="flex w-full items-center justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                  className="flex items-center gap-2"
                  disabled={isSaving}
                >
                  <X className="h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {isSaving ? "Saving..." : "Save"}
                </Button>
              </div>
            )}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}