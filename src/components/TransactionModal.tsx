import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Plus, TrendingUp, TrendingDown } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Transaction } from "@/hooks/useTransactions";

interface TransactionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddTransaction: (transaction: Omit<Transaction, "id" | "balance" | "created_at" | "updated_at">) => void;
}

export function TransactionModal({ isOpen, onClose, onAddTransaction }: TransactionModalProps) {
  const [type, setType] = useState<"money-in" | "money-out">("money-out");
  const [amount, setAmount] = useState("");
  const [note, setNote] = useState("");
  const [category, setCategory] = useState("");
  const [date, setDate] = useState<Date>(new Date());

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount || isNaN(Number(amount))) return;
    
    onAddTransaction({
      type,
      amount: Number(amount),
      note: note.trim() || null,
      category: category.trim() || null,
      date,
    });
    
    // Reset form
    setAmount("");
    setNote("");
    setCategory("");
    setDate(new Date());
    onClose();
  };

  const formatCurrency = (value: string) => {
    const number = value.replace(/\D/g, "");
    return new Intl.NumberFormat("vi-VN").format(Number(number));
  };

  const addMillion = () => {
    const currentValue = amount.replace(/\D/g, "");
    if (currentValue) {
      setAmount((Number(currentValue) * 1000000).toString());
    } else {
      setAmount("1000000");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md p-6">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add Transaction
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-3">
            <Label>Transaction Type</Label>
            <div className="grid grid-cols-2 gap-3">
              <Button
                type="button"
                variant={type === "money-in" ? "default" : "outline"}
                onClick={() => setType("money-in")}
                className={`h-12 flex items-center gap-2 ${
                  type === "money-in" 
                    ? "bg-success text-success-foreground hover:bg-success/90" 
                    : "hover:bg-success/10 hover:text-success hover:border-success"
                }`}
              >
                <TrendingUp className="w-4 h-4" />
                Money In
              </Button>
              <Button
                type="button"
                variant={type === "money-out" ? "destructive" : "outline"}
                onClick={() => setType("money-out")}
                className={`h-12 flex items-center gap-2 ${
                  type === "money-out" 
                    ? "" 
                    : "hover:bg-destructive/10 hover:text-destructive hover:border-destructive"
                }`}
              >
                <TrendingDown className="w-4 h-4" />
                Money Out
              </Button>
            </div>
          </div>

          <div className="space-y-3">
            <Label htmlFor="amount">Amount (VND)</Label>
            <div className="flex gap-2">
              <Input
                id="amount"
                type="text"
                placeholder="0"
                value={amount ? formatCurrency(amount) : ""}
                onChange={(e) => setAmount(e.target.value.replace(/\D/g, ""))}
                className="flex-1"
                required
              />
              <Button
                type="button"
                variant="outline"
                onClick={addMillion}
                className="px-3 text-xs whitespace-nowrap"
              >
                + triệu
              </Button>
            </div>
          </div>

          <div className="space-y-3">
            <Label>Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal h-11",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "dd/MM/yyyy") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(newDate) => newDate && setDate(newDate)}
                  initialFocus
                  className="p-3 pointer-events-auto"
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-3">
            <Label htmlFor="category">Category</Label>
            <Input
              id="category"
              type="text"
              placeholder="e.g., Food, Transport, Entertainment (optional)"
              value={category}
              onChange={(e) => setCategory(e.target.value)}
            />
          </div>

          <div className="space-y-3">
            <Label htmlFor="note">Note</Label>
            <Textarea
              id="note"
              placeholder="Add a note (optional)"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              rows={3}
              className="resize-none text-base"
            />
          </div>

          <div className="flex gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button type="submit" className="flex-1">
              Add Transaction
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}