-- Manual SQL script to be executed in Supabase SQL editor
-- This script creates the posts and comments tables for the Share functionality

-- Create posts table for sharing functionality
CREATE TABLE IF NOT EXISTS public.posts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_name TEXT NOT NULL,
  avatar TEXT,
  content TEXT NOT NULL,
  image TEXT,
  likes_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create comments table for post comments
CREATE TABLE IF NOT EXISTS public.post_comments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES public.posts(id) ON DELETE CASCADE,
  user_name TEXT NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- <PERSON>reate post_likes table to track likes (for future user-specific likes tracking)
CREATE TABLE IF NOT EXISTS public.post_likes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES public.posts(id) ON DELETE CASCADE,
  user_name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(post_id, user_name)
);

-- Enable Row Level Security
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_likes ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Enable read access for all users" ON public.posts;
DROP POLICY IF EXISTS "Enable insert for all users" ON public.posts;
DROP POLICY IF EXISTS "Enable update for all users" ON public.posts;
DROP POLICY IF EXISTS "Enable delete for all users" ON public.posts;
DROP POLICY IF EXISTS "Only admins can delete posts" ON public.posts;

DROP POLICY IF EXISTS "Enable read access for all users" ON public.post_comments;
DROP POLICY IF EXISTS "Enable insert for all users" ON public.post_comments;
DROP POLICY IF EXISTS "Enable update for all users" ON public.post_comments;
DROP POLICY IF EXISTS "Enable delete for all users" ON public.post_comments;
DROP POLICY IF EXISTS "Only admins can delete comments" ON public.post_comments;

DROP POLICY IF EXISTS "Enable read access for all users" ON public.post_likes;
DROP POLICY IF EXISTS "Enable insert for all users" ON public.post_likes;
DROP POLICY IF EXISTS "Enable delete for all users" ON public.post_likes;

-- Create policies for posts (allowing all operations for now)
CREATE POLICY "Enable read access for all users" ON public.posts
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for all users" ON public.posts
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for all users" ON public.posts
  FOR UPDATE USING (true);

CREATE POLICY "Only admins can delete posts" ON public.posts
  FOR DELETE TO authenticated
  USING (public.get_user_role(auth.uid()) = 'admin');

-- Create policies for comments
CREATE POLICY "Enable read access for all users" ON public.post_comments
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for all users" ON public.post_comments
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for all users" ON public.post_comments
  FOR UPDATE USING (true);

CREATE POLICY "Only admins can delete comments" ON public.post_comments
  FOR DELETE TO authenticated
  USING (public.get_user_role(auth.uid()) = 'admin');

-- Create policies for likes
CREATE POLICY "Enable read access for all users" ON public.post_likes
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for all users" ON public.post_likes
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable delete for all users" ON public.post_likes
  FOR DELETE USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON public.posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_post_comments_post_id ON public.post_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_post_comments_created_at ON public.post_comments(created_at ASC);
CREATE INDEX IF NOT EXISTS idx_post_likes_post_id ON public.post_likes(post_id);
CREATE INDEX IF NOT EXISTS idx_post_likes_user_name ON public.post_likes(user_name);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS update_posts_updated_at ON public.posts;
DROP TRIGGER IF EXISTS update_post_comments_updated_at ON public.post_comments;

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_posts_updated_at
  BEFORE UPDATE ON public.posts
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_post_comments_updated_at
  BEFORE UPDATE ON public.post_comments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Function to update likes count when likes are added/removed
CREATE OR REPLACE FUNCTION public.update_post_likes_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE public.posts 
    SET likes_count = likes_count + 1 
    WHERE id = NEW.post_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE public.posts 
    SET likes_count = likes_count - 1 
    WHERE id = OLD.post_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS update_post_likes_count_trigger ON public.post_likes;

-- Create trigger to automatically update likes count
CREATE TRIGGER update_post_likes_count_trigger
  AFTER INSERT OR DELETE ON public.post_likes
  FOR EACH ROW
  EXECUTE FUNCTION public.update_post_likes_count();

-- Insert sample posts data to replace the hardcoded data
INSERT INTO public.posts (id, user_name, avatar, content, likes_count, created_at) VALUES
  (
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'Nguyễn Văn A',
    '/placeholder.svg',
    'Hôm nay tôi đã tiết kiệm được 100,000 VND bằng cách nấu ăn tại nhà thay vì đi ăn ngoài. Cảm thấy rất hài lòng! 🍳✨ Ai cũng nên thử nhé!',
    24,
    now() - interval '2 hours'
  ),
  (
    'b2c3d4e5-f6g7-8901-bcde-f23456789012',
    'Lê Thị C',
    '/placeholder.svg',
    'Ai có mẹo nào để quản lý chi tiêu hàng tháng hiệu quả không? Tôi đang gặp khó khăn trong việc cân bằng thu chi. Mọi người chia sẻ kinh nghiệm giúp mình với! 🙏',
    18,
    now() - interval '5 hours'
  ),
  (
    'c3d4e5f6-g7h8-9012-cdef-345678901234',
    'Trần Minh F',
    '/placeholder.svg',
    'Vừa đạt được mục tiêu tiết kiệm 5 triệu trong tháng này! 🎉 Bí quyết là cắt giảm những chi tiêu không cần thiết và đầu tư vào những thứ thực sự quan trọng.',
    35,
    now() - interval '8 hours'
  )
ON CONFLICT (id) DO NOTHING;

-- Insert sample comments data
INSERT INTO public.post_comments (post_id, user_name, content, created_at) VALUES
  -- Comments for first post
  (
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'Trần Thị B',
    'Tuyệt vời! Nấu ăn tại nhà vừa tiết kiệm vừa healthy. Mình cũng đang áp dụng 👍',
    now() - interval '1 hour'
  ),
  (
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'Lê Minh C',
    'Wow, 100k một ngày là khá nhiều đấy! Mình cũng muốn học nấu ăn quá',
    now() - interval '45 minutes'
  ),
  -- Comments for second post
  (
    'b2c3d4e5-f6g7-8901-bcde-f23456789012',
    'Phạm Văn D',
    'Tôi dùng app này để theo dõi, rất hiệu quả! Nhớ phân loại chi tiêu từng mục nha',
    now() - interval '4 hours'
  ),
  (
    'b2c3d4e5-f6g7-8901-bcde-f23456789012',
    'Hoàng Thị E',
    'Nên lập kế hoạch chi tiêu từ đầu tháng và tuân thủ nghiêm ngặt. Rule 50/30/20 rất hay đó!',
    now() - interval '3 hours'
  ),
  -- Comments for third post
  (
    'c3d4e5f6-g7h8-9012-cdef-345678901234',
    'Nguyễn Thị G',
    'Chúc mừng bạn! Chia sẻ bí quyết cụ thể được không? 🤔',
    now() - interval '7 hours'
  )
ON CONFLICT DO NOTHING;

-- Add helpful comments
COMMENT ON TABLE public.posts IS 'Social posts for the sharing feature';
COMMENT ON COLUMN public.posts.user_name IS 'Name of the user who created the post';
COMMENT ON COLUMN public.posts.avatar IS 'Optional avatar URL for the user';
COMMENT ON COLUMN public.posts.content IS 'Text content of the post';
COMMENT ON COLUMN public.posts.image IS 'Optional image URL attached to the post';
COMMENT ON COLUMN public.posts.likes_count IS 'Number of likes for this post';

COMMENT ON TABLE public.post_comments IS 'Comments on social posts';
COMMENT ON COLUMN public.post_comments.post_id IS 'Reference to the post this comment belongs to';
COMMENT ON COLUMN public.post_comments.user_name IS 'Name of the user who made the comment';
COMMENT ON COLUMN public.post_comments.content IS 'Text content of the comment';

COMMENT ON TABLE public.post_likes IS 'Likes on social posts';
COMMENT ON COLUMN public.post_likes.post_id IS 'Reference to the post that was liked';
COMMENT ON COLUMN public.post_likes.user_name IS 'Name of the user who liked the post';
