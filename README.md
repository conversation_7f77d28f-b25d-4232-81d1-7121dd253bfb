
# Expense Management App

A simple **mobile-first expense management app** built with **React + Supabase**.  
The app helps you track **money in / money out**, monitor your balance, and analyze spending patterns.

---

## 🚀 Features

- **Authentication**
  - Login via Supabase Auth
  - Role-based access:
    - `admin`: full access (create, edit, delete transactions)
    - `user`: read-only access (view transactions and reports)

- **Transactions**
  - Add, edit, delete transactions (admin only)
  - View transaction history with balance updates
  - Mobile-friendly list view:
    - Icon for money-in / money-out
    - Note (with ellipsis if too long)
    - Date & time below note
    - Amount aligned to the right
  - Tap a row to see transaction details

- **Add Transaction Modal**
  - Quick selection between **Money In / Money Out**
  - Amount field with quick **“million” button** (adds `000000`)
  - Notes & date selection

- **Reports / Analytics**
  - Monthly money in/out chart
  - Top 3 biggest expenses
  - Balance history (line chart)
  - Insights into spending patterns to avoid negative balance

---

- **Supabase schema**
  - `profiles`: user account & role
  - `transactions`: expense records
  - Enum `app_role`: `admin` | `user`

---

## ⚙️ Tech Stack

- **Frontend:** React, TypeScript, TailwindCSS
- **Backend / DB:** Supabase (Postgres)
- **Auth:** Supabase Auth
- **Charts:** Recharts

---

## 🛡️ Security

- Row Level Security (RLS) enabled in Supabase
- Policies:
  - `SELECT`: all authenticated users
  - `INSERT/UPDATE/DELETE`: only `admin`

---

## 📦 Getting Started

1. Clone the repo:
```bash
   git clone https://github.com/yourname/expense-management.git
   cd expense-management
```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Setup environment variables:

   ```bash
   cp .env.example .env
   ```

   Add your Supabase `URL` and `ANON_KEY`.

4. Run the app:

   ```bash
   npm run dev
   ```

---

## 📈 Roadmap

* [ ] Add categories for transactions
* [ ] Export data to CSV/Excel
* [ ] Notifications for large expenses
* [ ] Multi-language support (English / Vietnamese / Japanese)

---

## 📜 License

MIT License
