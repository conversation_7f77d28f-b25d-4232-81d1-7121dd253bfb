# User Detail Functionality Documentation

## Overview
This document describes the new user detail functionality added to the UsersPage.tsx component, including clickable user navigation and detailed user information display.

## New Features Implemented

### 1. Clickable User Names
- **Location**: UsersPage.tsx table
- **Functionality**: User names in the table are now clickable buttons
- **Navigation**: Clicking a user name navigates to `/users/:userId/detail`
- **Styling**: Orange color scheme with hover effects for consistency

### 2. UserDetailPage Component
- **File**: `/src/pages/UserDetailPage.tsx`
- **Route**: `/users/:userId/detail`
- **Protection**: Protected by AdminRouteGuard (admin-only access)
- **Layout**: Two main sections as requested

#### Section 1: User Information (Top)
- **User Avatar**: Auto-generated initials-based avatar using dicebear API
- **Basic Information**: Name, email, role, status, last login, user type
- **Card Layout**: Consistent with existing design patterns
- **Responsive Design**: Works on mobile and desktop

#### Section 2: Access History Table (Bottom)
- **Data Source**: User access logs and activity history
- **Columns**: Timestamp, Action Type, Page/Location, IP Address
- **Features**: 
  - Refresh functionality
  - Loading states
  - Error handling
  - Color-coded action badges (Login, Logout, Page Visit)

### 3. Custom Hooks

#### useUserDetail Hook
- **File**: `/src/hooks/useUserDetail.ts`
- **Purpose**: Fetches individual user details
- **Data Sources**: 
  - Primary: Supabase profiles table for registered users
  - Primary: Supabase user_logs for guest users
  - Fallback: Mock data for demonstration
- **Features**:
  - Loading states
  - Error handling
  - Guest user support
  - Automatic refresh capability

#### useUserAccessHistory Hook
- **File**: `/src/hooks/useUserAccessHistory.ts`
- **Purpose**: Fetches user access history and activity logs
- **Data Sources**:
  - Primary: Supabase user_logs table
  - Fallback: Mock data with realistic access patterns
- **Features**:
  - Supports both registered and guest users
  - Rich mock data for demonstration
  - Pagination ready (limited to 50 records)
  - Error handling and loading states

## Technical Implementation

### Navigation Flow
```
UsersPage → Click User Name → UserDetailPage
/users → /users/:userId/detail
```

### Route Configuration
```typescript
// In App.tsx
<Route path="/users/:userId/detail" element={<UserDetailPage />} />
```

### Data Flow
```
UserDetailPage
├── useUserDetail(userId) → User basic information
└── useUserAccessHistory(userId) → Access history records
```

### TypeScript Interfaces

#### AccessHistoryRecord
```typescript
interface AccessHistoryRecord {
  id: string;
  timestamp: string;
  action: string;
  page?: string;
  location?: string;
  ipAddress?: string;
  userAgent?: string;
}
```

## UI Components Used

### Existing Components
- `AdminRouteGuard` - Route protection
- `Card`, `CardContent`, `CardHeader`, `CardTitle` - Layout structure
- `Button` - Navigation and actions
- `Badge` - Status and role indicators
- `Table` components - Access history display
- `Avatar` - User profile images

### Icons from Lucide React
- `ArrowLeft` - Back navigation
- `User`, `Mail`, `Shield`, `Clock` - User information icons
- `Eye`, `Calendar` - Access history icons

## Mock Data Features

### User Information Mock Data
- 7 sample users (admin, regular users, guests)
- Realistic timestamps and status information
- Guest user integration with localStorage

### Access History Mock Data
- 7 access records per user
- Various action types (login, logout, page_visit)
- Realistic IP addresses and user agents
- Different timestamps spanning multiple days

## Security Features

### Access Control
- **AdminRouteGuard**: Protects both UsersPage and UserDetailPage
- **Route Protection**: Direct URL access requires admin privileges
- **Navigation Guard**: Non-admin users can't see or access user details

### Data Privacy
- **Guest Users**: Limited information exposure
- **IP Addresses**: Currently mock data, ready for real implementation
- **Access Logging**: All page visits tracked for audit purposes

## Error Handling

### User Not Found
- Graceful error display with back navigation
- Fallback to mock data when Supabase is unavailable
- Clear error messages for debugging

### Network Errors
- Loading states during data fetching
- Retry mechanisms for failed requests
- Fallback to mock data for demonstration

## Responsive Design

### Mobile Support
- Responsive grid layouts
- Collapsible information sections
- Touch-friendly navigation buttons
- Optimized table display

### Desktop Experience
- Full-width layout utilization
- Hover effects and transitions
- Keyboard navigation support

## Future Enhancements

### Potential Features
- **Real-time Status**: WebSocket integration for live user status
- **Activity Filters**: Filter access history by date, action type
- **Export Functionality**: Export user data and access logs
- **User Management**: Edit user roles and permissions
- **Advanced Analytics**: User behavior patterns and insights

### Technical Improvements
- **Real IP Tracking**: Integrate actual IP address logging
- **Session Management**: Track user sessions and duration
- **Bulk Operations**: Multi-user management capabilities
- **Search Enhancement**: Advanced filtering in access history

## Usage Instructions

### For Admin Users
1. Navigate to Users page (`/users`)
2. Click on any user name in the table
3. View detailed user information in the top section
4. Review access history in the bottom table
5. Use the refresh button to update access history
6. Click "Back to Users" to return to the main users list

### Navigation Patterns
- **Forward**: Users List → User Detail
- **Backward**: User Detail → Users List
- **Direct Access**: `/users/[userId]/detail` (admin-only)

## Integration Points

### Existing Hooks
- Integrates with existing `useAuth` for admin checking
- Uses `useAccessLogging` for audit trail
- Leverages `UserData` interface from `useUsers`

### Supabase Integration
- Reads from `profiles` table for user information
- Reads from `user_logs` table for access history
- Graceful fallback when database is unavailable

## Testing Considerations

### Test Scenarios
1. **Admin Access**: Verify admin users can access all features
2. **Non-admin Restriction**: Ensure non-admin users are redirected
3. **Guest User Details**: Test guest user information display
4. **Registered User Details**: Test registered user information display
5. **Error Handling**: Test with invalid user IDs
6. **Navigation**: Test back and forth navigation flows
7. **Responsive**: Test on various screen sizes
8. **Data Loading**: Test with slow network conditions
